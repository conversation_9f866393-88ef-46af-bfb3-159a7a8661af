<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.patrol.mapper.PatrolAssetCheckMapper">
    <resultMap type="com.ruoyi.patrol.domain.PatrolAssetCheck" id="PatrolAssetCheckResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="category" column="category"/>
        <result property="kahunaId" column="kahuna_id"/>
        <result property="kahunaName" column="kahuna_name"/>
        <result property="kahunaSign" column="kahuna_sign"/>
        <result property="oprUserId" column="opr_user_id"/>
        <result property="oprUserName" column="opr_user_name"/>
        <result property="oprUserSign" column="opr_user_sign"/>
        <result property="checkTime" column="check_time"/>
        <result property="status" column="status"/>
        <result property="auditTime" column="audit_time"/>
        <result property="image" column="image"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="assetId" column="asset_id"/>
        <result property="assetName" column="asset_name"/>
        <result property="assetCode" column="asset_code"/>
        <result property="routeId" column="route_id"/>
        <result property="routeName" column="route_name"/>
        <result property="routeCode" column="route_code"/>
        <result property="propertyUnitId" column="property_unit_id"/>
        <result property="propertyUnitName" column="property_unit_name"/>
        <result property="maintainUnitId" column="maintain_unit_id"/>
        <result property="maintainUnitName" column="maintain_unit_name"/>
        <result property="maintenanceSectionId" column="maintenance_section_id"/>
        <result property="maintenanceSectionName" column="maintenance_section_name"/>
        <result property="centerStake" column="center_stake"/>
        <result property="weather" column="weather"/>
        <result property="expiry" column="expiry"/>
        <result property="frequency" column="frequency"/>
        <result property="stage" column="stage"/>
        <result property="diseaseCount" column="disease_count"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <resultMap type="com.ruoyi.patrol.domain.PatrolCheckBase" id="PatrolCheckBaseResult">
        <result property="type" column="type"/>
        <result property="assetId" column="asset_id"/>
        <result property="expiry" column="expiry"/>
        <result property="frequency" column="frequency"/>
    </resultMap>

    <sql id="base_column">
        id
        ,
        type,
        category,
        kahuna_id,
        kahuna_name,
        kahuna_sign,
        opr_user_id,
        opr_user_name,
        opr_user_sign,
        check_time,
        status,
        audit_time,
        image,
        remark,
        del_flag,
        asset_id,
        asset_name,
        asset_code,
        route_id,
        route_name,
        route_code,
        property_unit_id,
        property_unit_name,
        maintain_unit_id,
        maintain_unit_name,
        maintenance_section_id,
        maintenance_section_name,
        center_stake,
        weather,
        expiry,
        frequency,
        stage,
        disease_count,
        create_time,
        update_time,
        create_by,
        update_by
    </sql>

    <sql id="where_column">
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
        <if test="category != null and category != ''">
            AND category = #{category}
        </if>
        <if test="kahunaId != null and kahunaId != ''">
            AND kahuna_id = #{kahunaId}
        </if>
        <if test="kahunaName != null and kahunaName != ''">
            AND kahuna_name = #{kahunaName}
        </if>
        <if test="kahunaSign != null and kahunaSign != ''">
            AND kahuna_sign = #{kahunaSign}
        </if>
        <if test="oprUserId != null and oprUserId != ''">
            AND opr_user_id = #{oprUserId}
        </if>
        <if test="oprUserName != null and oprUserName != ''">
            AND opr_user_name = #{oprUserName}
        </if>
        <if test="oprUserSign != null and oprUserSign != ''">
            AND opr_user_sign = #{oprUserSign}
        </if>
        <if test="checkTime != null and checkTime != ''">
            AND check_time = #{checkTime}
        </if>
        <if test="checkTimee != null and checkTimee != ''">
            AND date_format(check_time, '%Y-%m-%d') <![CDATA[>=]]> date_format(
            #{checkTimee}, '%Y-%m-%d')
        </if>
        <if test="checkTimes != null and checkTimes != ''">
            AND date_format(check_time, '%Y-%m-%d') <![CDATA[<=]]> date_format(
            #{checkTimes}, '%Y-%m-%d')
        </if>
        <if test="status != null and status != ''">
            AND status =
            #{status}
        </if>
        <if test="auditTime != null and auditTime != ''">
            AND audit_time = #{auditTime}
        </if>
        <if test="image != null and image != ''">
            AND image = #{image}
        </if>
        <if test="remark != null and remark != ''">
            AND remark = #{remark}
        </if>
        <if test="delFlag != null and delFlag != ''">
            AND del_flag = #{delFlag}
        </if>
        <if test="assetId != null and assetId != ''">
            AND asset_id = #{assetId}
        </if>
        <if test="assetName != null and assetName != ''">
            AND asset_name = #{assetName}
        </if>
        <if test="assetCode != null and assetCode != ''">
            AND asset_code = #{assetCode}
        </if>
        <if test="routeId != null and routeId != ''">
            AND route_id = #{routeId}
        </if>
        <if test="routeName != null and routeName != ''">
            AND route_name = #{routeName}
        </if>
        <if test="routeCode != null and routeCode != ''">
            AND route_code = #{routeCode}
        </if>
        <if test="propertyUnitId != null and propertyUnitId != ''">
            AND property_unit_id = #{propertyUnitId}
        </if>
        <if test="propertyUnitName != null and propertyUnitName != ''">
            AND property_unit_name = #{propertyUnitName}
        </if>
        <if test="maintainUnitId != null and maintainUnitId != ''">
            AND maintain_unit_id = #{maintainUnitId}
        </if>
        <if test="maintainUnitName != null and maintainUnitName != ''">
            AND maintain_unit_name = #{maintainUnitName}
        </if>
        <if test="maintenanceSectionId != null and maintenanceSectionId != ''">
            AND maintenance_section_id = #{maintenanceSectionId}
        </if>
        <if test="maintenanceSectionName != null and maintenanceSectionName != ''">
            AND maintenance_section_name = #{maintenanceSectionName}
        </if>
        <if test="centerStake != null and centerStake != ''">
            AND center_stake = #{centerStake}
        </if>
        <if test="weather != null and weather != ''">
            AND weather = #{weather}
        </if>
        <if test="expiry != null and expiry != ''">
            AND expiry = #{expiry}
        </if>
        <if test="frequency != null and frequency != ''">
            AND frequency = #{frequency}
        </if>
        <if test="stage != null and stage != ''">
            AND stage = #{stage}
        </if>
        <if test="diseaseCount != null and diseaseCount != ''">
            AND disease_count = #{diseaseCount}
        </if>
        <if test="createTime != null and createTime != ''">
            AND create_time = #{createTime}
        </if>
        <if test="updateTime != null and updateTime != ''">
            AND update_time = #{updateTime}
        </if>
        <if test="createBy != null and createBy != ''">
            AND create_by = #{createBy}
        </if>
        <if test="updateBy != null and updateBy != ''">
            AND update_by = #{updateBy}
        </if>
    </sql>

    <!-- 批量更新病害数量 -->
    <update id="updateDiseaseNumbers">
        UPDATE ${tableName}
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="disease_count = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} THEN #{item.diseaseNum}
                </foreach>
            </trim>
        </trim>
        WHERE id IN

        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <!-- 批量更新到期时间 -->
    <update id="updateExpiryBatch">
        UPDATE ${tableName}
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="expiry = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} THEN #{item.expiry}
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <!-- 批量更新管养分处ID和名称 -->
    <update id="updatePropertyUnitBatch">
        UPDATE ${tableName}
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="property_unit_id = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} THEN #{item.propertyUnitId}
                </foreach>
            </trim>
            <trim prefix="property_unit_name = CASE" suffix="END,">
                <foreach collection="list" item="item">
                    WHEN id = #{item.id} THEN #{item.propertyUnitName}
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <!-- 根据ID列表批量更新状态 -->
    <update id="updateStatusByIds">
        update ${tableName}
        set status = #{entity.status},
            audit_time = #{entity.auditTime},
            kahuna_id = #{entity.kahunaId},
            kahuna_name = #{entity.kahunaName},
            kahuna_sign = #{entity.kahunaSign}
        where id in
        <foreach collection="checkIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 通过参数查找单条 -->
    <select id="findByParam" resultMap="PatrolAssetCheckResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_asset_check
        <where>
            <include refid="where_column"/>
        </where>
    </select>

    <!-- 通过参数查找集合 -->
    <select id="findListByParam" resultMap="PatrolAssetCheckResult">
        SELECT
        <include refid="base_column"/>
        FROM patrol_asset_check
        <where>
            <include refid="where_column"/>
        </where>
        <if test="order != null and order != ''">
            order by ${order}
        </if>
        <if test="pageNum != null and pageNum >= 0 and pageSize != null and pageSize > 0">
            limit #{pageNum},#{pageSize}
        </if>
    </select>
    <!-- 查询 patrol_asset_check 及其关联的 patrol_asset_check_detail -->
    <select id="selectPatrolAssetChecksByAssetIds" resultMap="PatrolAssetCheckDTOMap"
            parameterType="com.ruoyi.patrol.domain.request.PatrolAssetCheckRequest">
        SELECT pac.id AS id,
        pac.type AS type,
        pac.category AS category,
        pac.kahuna_id AS kahunaId,
        pac.kahuna_name AS kahunaName,
        pac.kahuna_sign AS kahunaSign,
        pac.opr_user_id AS oprUserId,
        pac.opr_user_name AS oprUserName,
        pac.opr_user_sign AS oprUserSign,
        pac.check_time AS checkTime,
        pac.status AS status,
        pac.audit_time AS auditTime,
        pac.image AS image,
        pac.remark AS remark,
        pac.del_flag AS delFlag,
        pac.asset_id AS assetId,
        pac.asset_name AS assetName,
        pac.asset_code AS assetCode,
        pac.route_id AS routeId,
        pac.route_name AS routeName,
        pac.route_code AS routeCode,
        pac.property_unit_id AS propertyUnitId,
        pac.property_unit_name AS propertyUnitName,
        pac.maintain_unit_id AS maintainUnitId,
        pac.maintain_unit_name AS maintainUnitName,
        pac.maintenance_section_id AS maintenanceSectionId,
        pac.maintenance_section_name AS maintenanceSectionName,
        pac.center_stake AS centerStake,
        pac.weather AS weather,
        pac.expiry AS expiry,
        pac.frequency AS frequency,
        pac.stage AS stage,
        pac.disease_count AS diseaseCount,
        pac.create_time AS createTime,
        pac.update_time AS updateTime,
        pac.create_by AS createBy,
        pac.update_by AS updateBy,
        pacd.id AS detail_id,
        pacd.check_id AS checkId,
        pacd.parts_type_id AS partsTypeId,
        pacd.parts_type_name AS partsTypeName,
        pacd.defect AS defect,
        pacd.advice AS advice,
        pacd.image AS detailImage,
        pacd.del_flag AS detailDelFlag,
        pacd.des AS des
        FROM patrol_asset_check pac
        LEFT JOIN
        patrol_asset_check_detail pacd
        ON
        pac.id = pacd.check_id
        WHERE 1 = 1
        <if test="assetIdList != null and !assetIdList.isEmpty()">
            AND pac.asset_id IN
            <foreach item="assetId" index="index" collection="assetIdList" open="(" separator="," close=")">
                #{assetId}
            </foreach>
        </if>
        <if test="typeCode != null and typeCode != ''">
            AND pac.type = #{typeCode}
        </if>
        <if test="lastCheckTime != null">
            AND DATE (pac.check_time)
            &gt; #{lastCheckTime}
        </if>
        <if test="nowCheckTime != null">
            AND DATE (pac.check_time) &lt;= #{nowCheckTime}
        </if>
    </select>

    <select id="selectPatrolAssetChecksByAssetIdsBatch" resultMap="PatrolAssetCheckDTOMap"
            parameterType="java.util.List">
        <bind name="detailTableName" value="tableName + '_detail'"/>
        SELECT pac.id AS id,
        pac.type AS type,
        pac.category AS category,
        pac.kahuna_id AS kahunaId,
        pac.kahuna_name AS kahunaName,
        pac.kahuna_sign AS kahunaSign,
        pac.opr_user_id AS oprUserId,
        pac.opr_user_name AS oprUserName,
        pac.opr_user_sign AS oprUserSign,
        pac.check_time AS checkTime,
        pac.status AS status,
        pac.audit_time AS auditTime,
        pac.image AS image,
        pac.remark AS remark,
        pac.del_flag AS delFlag,
        pac.asset_id AS assetId,
        pac.asset_name AS assetName,
        pac.asset_code AS assetCode,
        pac.route_id AS routeId,
        pac.route_name AS routeName,
        pac.route_code AS routeCode,
        pac.property_unit_id AS propertyUnitId,
        pac.property_unit_name AS propertyUnitName,
        pac.maintain_unit_id AS maintainUnitId,
        pac.maintain_unit_name AS maintainUnitName,
        pac.maintenance_section_id AS maintenanceSectionId,
        pac.maintenance_section_name AS maintenanceSectionName,
        pac.center_stake AS centerStake,
        pac.weather AS weather,
        pac.expiry AS expiry,
        pac.frequency AS frequency,
        pac.stage AS stage,
        pac.disease_count AS diseaseCount,
        pac.create_time AS createTime,
        pac.update_time AS updateTime,
        pac.create_by AS createBy,
        pac.update_by AS updateBy,
        pacd.id AS detail_id,
        pacd.check_id AS checkId,
        pacd.parts_type_id AS partsTypeId,
        pacd.parts_type_name AS partsTypeName,
        pacd.defect AS defect,
        pacd.advice AS advice,
        pacd.image AS detailImage,
        pacd.del_flag AS detailDelFlag,
        pacd.des AS des
        FROM ${tableName} pac
        LEFT JOIN
        ${detailTableName} pacd
        ON
        pac.id = pacd.check_id
        WHERE 1 = 1
        <if test="type != null and type != ''">
            AND pac.type = #{type}
        </if>
        <if test="nowCheckTime != null">
            AND DATE (pac.check_time) &lt;= #{nowCheckTime}
        </if>
        <foreach item="requestDTO" collection="list" open="AND (" separator=" OR " close=")">
            (
            <if test="requestDTO.assetIdList != null and !requestDTO.assetIdList.isEmpty()">
                pac.asset_id IN
                <foreach item="assetId" index="index" collection="requestDTO.assetIdList" open="(" separator=","
                         close=")">
                    #{assetId}
                </foreach>
            </if>
            <if test="requestDTO.lastCheckTime != null">
                AND DATE (pac.check_time)
                &gt; #{requestDTO.lastCheckTime}
            </if>
            )
        </foreach>
    </select>

    <!-- PatrolAssertCheckMapper.xml -->
    <!--    <select id="selectCheckedDaysAssetIds" resultType="string">-->
    <!--        WITH temp_filtered_assets_day AS (-->
    <!--        SELECT asset_id-->
    <!--        FROM ${tableName}-->
    <!--        WHERE DATE(DATE_SUB(expiry, INTERVAL frequency DAY)) &lt; DATE(#{now})-->
    <!--        AND DATE(#{now}) &lt;= DATE(expiry)-->
    <!--        AND type = #{type}-->
    <!--        )-->
    <!--        SELECT asset_id-->
    <!--        FROM temp_filtered_assets_day-->
    <!--        WHERE asset_id IN-->
    <!--        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">-->
    <!--            #{item}-->
    <!--        </foreach>-->
    <!--    </select>-->


    <!--    <select id="selectCheckedMonthAssetIds" resultType="string">-->
    <!--        WITH temp_filtered_assets_month AS (-->
    <!--        SELECT asset_id-->
    <!--        FROM ${tableName}-->
    <!--        WHERE DATE_FORMAT(DATE_SUB(expiry, INTERVAL frequency MONTH), '%Y-%m') &lt; #{yearMonth}-->
    <!--        AND #{yearMonth} &lt;= DATE_FORMAT(expiry, '%Y-%m')-->
    <!--        AND type = #{type}-->
    <!--        )-->
    <!--        SELECT asset_id-->
    <!--        FROM temp_filtered_assets_month-->
    <!--        WHERE asset_id IN-->
    <!--        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">-->
    <!--            #{item}-->
    <!--        </foreach>-->
    <!--    </select>-->

    <select id="selectLatestExpiry" resultType="map">
        SELECT asset_id, MAX(expiry)
        FROM ${tableName}
        WHERE type = #{type}
        AND
        asset_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY asset_id
    </select>

    <select id="findLatestExpiry" resultType="java.time.LocalDateTime">
        SELECT MAX(expiry)
        FROM ${tableName}
        WHERE type = #{type}
          AND asset_id = #{assetId}
    </select>


    <select id="findExpiryResults" resultMap="PatrolCheckBaseResult">
        SELECT asset_id,
               expiry,
               frequency,
               type
        FROM (SELECT asset_id,
                     expiry,
                     frequency,
                     type
              FROM ${tableName}
              WHERE type = #{type}
                AND asset_id = #{assetId}
                AND expiry &gt;= #{date}
              ORDER BY expiry ASC LIMIT 1) AS firstExpiryAfterOrEqualDate
        UNION ALL
        SELECT asset_id,
               expiry,
               frequency,
               type
        FROM (SELECT asset_id,
                     expiry,
                     frequency,
                     type
              FROM ${tableName}
              WHERE type = #{type}
                AND asset_id = #{assetId}
                AND expiry &lt;= #{date}
              ORDER BY expiry DESC LIMIT 1) AS firstExpiryBeforeOrEqualDate
    </select>

    <!--    <select id="findExpiryResultsBatch" resultMap="PatrolCheckBaseResult">-->
    <!--        SELECT asset_id,-->
    <!--               expiry,-->
    <!--               frequency,-->
    <!--               type-->
    <!--        FROM (-->
    <!--        SELECT asset_id,-->
    <!--               expiry,-->
    <!--               frequency,-->
    <!--               type-->
    <!--        FROM ${tableName}-->
    <!--        WHERE type = #{type}-->
    <!--          AND asset_id IN-->
    <!--        <foreach collection="assetIds" item="assetId" open="(" separator="," close=")">-->
    <!--            #{assetId}-->
    <!--        </foreach>-->
    <!--        AND expiry &gt;= #{date}-->
    <!--        ORDER BY asset_id, expiry ASC-->
    <!--        ) AS firstExpiryAfterOrEqualDate-->
    <!--        UNION ALL-->
    <!--        SELECT asset_id,-->
    <!--               expiry,-->
    <!--               frequency,-->
    <!--               type-->
    <!--        FROM (-->
    <!--        SELECT asset_id,-->
    <!--               expiry,-->
    <!--               frequency,-->
    <!--               type-->
    <!--        FROM ${tableName}-->
    <!--        WHERE type = #{type}-->
    <!--          AND asset_id IN-->
    <!--        <foreach collection="assetIds" item="assetId" open="(" separator="," close=")">-->
    <!--            #{assetId}-->
    <!--        </foreach>-->
    <!--        AND expiry &lt;= #{date}-->
    <!--        ORDER BY asset_id, expiry DESC-->
    <!--        ) AS firstExpiryBeforeOrEqualDate-->
    <!--    </select>-->

    <select id="findExpiryResultsBatch" resultMap="PatrolCheckBaseResult">
        WITH RankedRecords AS (
        SELECT asset_id,
        expiry,
        frequency,
        type,
        ROW_NUMBER() OVER (
        PARTITION BY asset_id,
        CASE
        WHEN expiry >= #{date} THEN 'after'
        ELSE 'before'
        END
        ORDER BY
        CASE
        WHEN expiry >= #{date} THEN ABS(DATEDIFF(expiry, #{date}))
        ELSE ABS(DATEDIFF(expiry, #{date}))
        END ASC
        ) as rn
        FROM ${tableName}
        WHERE type = #{type}
        AND asset_id IN
        <foreach collection="assetIds" item="assetId" open="(" separator="," close=")">
            #{assetId}
        </foreach>
        )
        SELECT asset_id,
        expiry,
        frequency,
        type
        FROM RankedRecords
        WHERE rn = 1
        ORDER BY asset_id, expiry
    </select>


    <select id="selectCheckedDaysAssetIds" resultType="string">
        SELECT asset_id
        FROM (SELECT asset_id
        FROM ${tableName}
        WHERE expiry &lt; DATE_ADD(#{now}, INTERVAL frequency DAY)
        AND #{now} &lt;= expiry
        AND type = #{type}
        <if test="status != null">
            AND status = #{status}
        </if>
        ) AS temp_filtered_assets_day
        WHERE asset_id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectCheckedMonthAssetIds" resultType="com.ruoyi.patrol.domain.PatrolAssetCheck">
        SELECT asset_id, id, stage
        FROM (SELECT asset_id,
        id,
        stage
        FROM ${tableName}
        WHERE expiry &gt;= #{yearMonth}
        AND expiry &lt; DATE_ADD(#{yearMonth}, INTERVAL frequency MONTH)
        AND type = #{type}
        <if test="status != null">
            AND status = #{status}
        </if>
        <!--                AND stage != 0-->
        ) AS temp_filtered_assets_month
        WHERE asset_id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectCheckedMonthAssetIdsByJD" resultType="string">
        SELECT asset_id
        FROM (SELECT asset_id
        FROM ${tableName}
        WHERE expiry &gt;= #{yearMonth}
        AND expiry &lt; DATE_ADD(#{yearMonth}, INTERVAL frequency MONTH)
        AND type = #{type}
        <if test="status != null">
            AND status = #{status}
        </if>
        ) AS temp_filtered_assets_month
        WHERE asset_id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectCheckedYear" resultMap="PatrolAssetCheckResult">
        SELECT DISTINCT
        <include refid="base_column"/>
        FROM (
        /* 基础数据集查询 */
        SELECT
        <include refid="base_column"/>
        FROM ${tableName}
        <where>
            <if test="type != null">
                type = #{type}
            </if>
            <if test="list != null and !list.isEmpty()">
                AND asset_id IN
                <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ) base_data
        WHERE
        /* 年份匹配条件 */
        YEAR (expiry) = #{year}
        OR
        /* 补充查询条件 - 通常只有少量记录满足 */
        (
        YEAR (DATE_SUB(expiry
        , INTERVAL frequency MONTH)) = #{year}
        AND YEAR (DATE_SUB(expiry
        , INTERVAL 1 YEAR)) = #{year}
        )
        /* 添加排序条件：先按asset_id升序，再按expiry降序 */
        ORDER BY id ASC, expiry DESC
    </select>


    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO ${tableName} (id, type, category, kahuna_id, kahuna_name, kahuna_sign, opr_user_id, opr_user_name,
        opr_user_sign,
        check_time, status, audit_time, image, remark, del_flag, asset_id, asset_name,
        asset_code, route_id, route_name, route_code, property_unit_id, property_unit_name,
        maintain_unit_id, maintain_unit_name, maintenance_section_id,
        maintenance_section_name, center_stake, weather,
        disease_count,
        expiry, frequency, stage,
        create_time, update_time, create_by, update_by) VALUES
        <foreach collection="list" item="item" separator=",">
            ( #{item.id}, #{item.type}, #{item.category}, #{item.kahunaId}, #{item.kahunaName}, #{item.kahunaSign},
            #{item.oprUserId}, #{item.oprUserName}, #{item.oprUserSign}, #{item.checkTime},
            #{item.status}, #{item.auditTime}, #{item.image}, #{item.remark}, #{item.delFlag},
            #{item.assetId}, #{item.assetName}, #{item.assetCode}, #{item.routeId}, #{item.routeName},
            #{item.routeCode}, #{item.propertyUnitId}, #{item.propertyUnitName},
            #{item.maintainUnitId}, #{item.maintainUnitName}, #{item.maintenanceSectionId},
            #{item.maintenanceSectionName}, #{item.centerStake}, #{item.weather},
            #{item.diseaseCount},
            #{item.expiry}, #{item.frequency}, #{item.stage},
            #{item.createTime}, #{item.updateTime}, #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <!-- 日常巡查对应的月有记录的日期 -->
    <select id="findByAssetIdAndYearMonth" resultMap="PatrolAssetCheckResult" parameterType="map">
        SELECT
        <include refid="base_column"/>
        FROM ${tableName}
        WHERE asset_id = #{assetId}
        AND type = #{type}
        AND (
        DATE_FORMAT(expiry, '%Y-%m') = #{yearMonth}
        OR DATE_FORMAT(DATE_SUB(expiry, INTERVAL frequency DAY) + INTERVAL 1 DAY, '%Y-%m') = #{yearMonth}
        )
    </select>

    <!-- 经常检查对应的有记录的日期 -->
    <select id="findByAssetIdAndYear" resultMap="PatrolAssetCheckResult" parameterType="map">
        SELECT
        <include refid="base_column"/>
        FROM patrol_asset_check
        WHERE asset_id = #{assetId}
        AND type = #{type}
        AND (
        DATE_FORMAT(expiry, '%Y') = #{year}
        OR DATE_FORMAT(DATE_SUB(expiry, INTERVAL frequency MONTH) + INTERVAL 1 MONTH, '%Y') = #{year}
        )
    </select>

    <!-- 定义公共的 WHERE 条件 -->
    <sql id="Common_Where_Clause">
        <where>
            <!-- assetId条件 -->
            <if test="request.assetId != null and request.assetId != ''">
                AND pac.asset_id = #{request.assetId}
            </if>
            <if test="request.assetName != null and request.assetName != ''">
                AND pac.asset_name LIKE CONCAT('%'
                , #{request.assetName}
                , '%')
            </if>
            <if test="request.assetCode != null and request.assetCode != ''">
                AND pac.asset_code LIKE CONCAT('%'
                , #{request.assetCode}
                , '%')
            </if>
            <if test="request.type != null">
                AND pac.type = #{request.type}
            </if>
            <!-- 路线编码条件 -->
            <if test="request.routeCode != null and request.routeCode != ''">
                AND pac.route_code = #{request.routeCode}
            </if>
            <if test="request.routeCodes != null and request.routeCodes.size() > 0">
                AND pac.route_code IN
                <foreach collection="request.routeCodes" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>

            <!-- 养护段条件 -->
            <if test="request.maintenanceSectionId != null and request.maintenanceSectionId != ''">
                AND pac.maintenance_section_id = #{request.maintenanceSectionId}
            </if>
            <if test="request.maintenanceSectionIdList != null and request.maintenanceSectionIdList.size() > 0">
                AND pac.maintenance_section_id IN
                <foreach collection="request.maintenanceSectionIdList" item="sectionId" open="(" separator=","
                         close=")">
                    #{sectionId}
                </foreach>
            </if>

            <!-- ID条件 -->
            <if test="request.ids != null">
                <choose>
                    <when test="request.ids.size() > 0">
                        AND pac.asset_id IN
                        <foreach collection="request.ids" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1 = 0
                    </otherwise>
                </choose>
            </if>

            <!-- 排除ID条件 -->
            <if test="request.excludeIds != null and request.excludeIds.size() > 0">
                AND pac.asset_id NOT IN
                <foreach collection="request.excludeIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            <!-- 状态条件 -->
            <if test="request.status != null">
                AND pac.status = #{request.status}
            </if>
            <if test="request.statusList != null and request.statusList.size() > 0">
                AND pac.status IN
                <foreach collection="request.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>

            <!-- 检查时间 -->
            <if test="request.checkTime != null">
                <choose>
                    <when test="request.isDailyCheck == false">
                        AND pac.check_time &gt;= #{request.checkTime}
                        AND pac.check_time
                        &lt; DATE_ADD(#{request.checkTime}
                        , INTERVAL 1 MONTH)
                    </when>
                    <otherwise>
                        AND pac.check_time &gt;= #{request.checkTime}
                        AND pac.check_time
                        &lt; DATE_ADD(#{request.checkTime}
                        , INTERVAL 1 DAY)
                    </otherwise>
                </choose>
            </if>
            <!-- 检查时间条件: isDailyCheck为false时截取到月，其他情况截取到日 -->
            <if test="request.checkStartTime != null">
                AND pac.check_time &gt;=#{request.checkStartTime}
            </if>

            <if test="request.checkEndTime != null">
                AND pac.check_time
                &lt; #{request.checkEndTime}
            </if>

            <!-- 到期时间 -->
            <if test="request.expiry != null">
                <choose>
                    <when test="request.isDailyCheck == false">
                        AND pac.expiry &gt;= #{request.expiry}
                        AND pac.expiry
                        &lt; DATE_ADD(#{request.expiry}
                        , INTERVAL 1 MONTH)
                    </when>
                    <otherwise>
                        AND pac.expiry &gt;= #{request.expiry}
                        AND pac.expiry
                        &lt; DATE_ADD(#{request.expiry}
                        , INTERVAL 1 DAY)
                    </otherwise>
                </choose>
            </if>

            <!-- 到期时间范围 -->
            <if test="request.expiryStartTime != null">
                AND pac.expiry &gt;= #{request.expiryStartTime}
            </if>

            <if test="request.expiryEndTime != null">
                AND pac.expiry &lt;= #{request.expiryEndTime}
            </if>

            <!--  管养单位 -->
            <if test="request.managementMaintenanceId != null and request.managementMaintenanceId != ''">
                AND pac.maintain_unit_id = #{request.managementMaintenanceId}
            </if>
            <if test="request.managementMaintenanceIds != null and request.managementMaintenanceIds.size() > 0">
                AND pac.maintain_unit_id IN
                <foreach collection="request.managementMaintenanceIds" item="maintenanceId" open="(" separator=","
                         close=")">
                    #{maintenanceId}
                </foreach>
            </if>
            <if test="request.managementMaintenanceName != null and request.managementMaintenanceName != ''">
                AND pac.property_unit_name leke CONCAT('%'
                , #{request.managementMaintenanceName}
                , '%')
            </if>

            <!-- 管养分处 -->
            <if test="request.managementMaintenanceBranchId != null and request.managementMaintenanceBranchId != ''">
                AND pac.property_unit_id = #{request.managementMaintenanceBranchId}
            </if>
            <if test="request.managementMaintenanceBranchIds != null and request.managementMaintenanceBranchIds.size() > 0">
                AND pac.property_unit_id IN
                <foreach collection="request.managementMaintenanceBranchIds" item="maintenanceBranchId" open="("
                         separator="," close=")">
                    #{maintenanceBranchId}
                </foreach>
            </if>
            <if test="request.managementMaintenanceBranchName != null and request.managementMaintenanceBranchName != ''">
                AND pac.maintain_unit_name LIKE CONCAT('%'
                , #{request.managementMaintenanceBranchName}
                , '%')
            </if>

            <!-- 自定义SQL条件 - 允许直接传入SQL条件片段 -->
            <if test="request.customSqlCondition != null and request.customSqlCondition != ''">
                AND ${request.customSqlCondition}
            </if>
            <!-- 添加stage条件  -->
            <if test="request.stage != null">
                AND pac.stage = #{request.stage}
            </if>

            <!-- 病害数量条件 -->
            <if test="request.hasDisease != null">
                <choose>
                    <when test="request.hasDisease == true">
                        AND pac.disease_count > 0
                    </when>
                    <otherwise>
                        AND (pac.disease_count = 0 OR pac.disease_count IS NULL OR pac.disease_count = '')
                    </otherwise>
                </choose>
            </if>

            <!-- 关键字搜索条件 -->
            <if test="request.ks != null">
                AND (pac.asset_name LIKE CONCAT('%'
                , #{request.ks}
                , '%')
                OR pac.asset_code LIKE CONCAT('%'
                , #{request.ks}
                , '%'))
            </if>
            <if test="request.isCheck != null and request.type != null and request.type.code in ('2', '4', '6')">
                <if test="request.isCheck == true">
                    AND pac.stage = 1
                </if>
                <if test="request.isCheck == false">
                    AND pac.stage = 0
                </if>
            </if>
            <if test="request.checkId != null and request.checkId != null">
                AND pac.id = #{request.checkId}
            </if>
            <if test="request.checkIds != null and request.checkIds.size() > 0">
                AND pac.id IN
                <foreach collection="request.checkIds" item="checkId" open="(" separator="," close=")">
                    #{checkId}
                </foreach>
            </if>
        </where>
    </sql>

    <!-- 时间范围内按照管养单位和养护段统计 -->
    <!-- <select id="selectMonthlyStatistics" resultType="com.ruoyi.patrol.domain.vo.AssetCheckStatisticsVO">
        SELECT maintain_unit_name       AS managementOffice,
               maintain_unit_id         AS managementOfficeId,
               maintenance_section_name AS sectionName,
               maintenance_section_id   AS sectionId,
               #{month} AS month,
               COUNT(1)          AS totalCount,
               SUM(CASE WHEN stage = 1 THEN 1 ELSE 0 END) AS inPeriodCount,
               SUM(CASE WHEN stage = 0 THEN 1 ELSE 0 END) AS completedCount
        FROM ${tableName}
        <include refid="Common_Where_Clause"/>
        GROUP BY
            maintain_unit_name,
            maintain_unit_id,
            maintenance_section_name,
            maintenance_section_id
    </select> -->

    <select id="selectMonthlyStatistics" resultType="com.ruoyi.patrol.domain.vo.AssetCheckStatisticsVO">
        SELECT pac.maintain_unit_name AS managementOffice,
        pac.maintenance_section_name AS sectionName,
        #{month} AS month, #{formatTime} AS formatTime, COUNT (1) AS totalCount, SUM (CASE WHEN pac.stage = 0 THEN 1 ELSE 0
        END) AS inPeriodCount, SUM (CASE WHEN pac.stage = 1 THEN 1 ELSE 0 END) AS completedCount
        FROM ${tableName} pac
        <include refid="Common_Where_Clause"/>
        GROUP BY pac.maintain_unit_name, pac.maintenance_section_name
    </select>

    <!-- 查询总数 -->
    <select id="countAssetCheckData" resultType="int">
        SELECT COUNT(1)
        FROM ${tableName} pac
        <include refid="Common_Where_Clause"/>
    </select>

    <!-- 查询数据 -->
    <select id="selectAssetCheckData" resultMap="PatrolAssetCheckResult">
        <choose>
            <!-- 当 offset 和 pageSize 不为空时，使用分页的 INNER JOIN 模式 -->
            <when test="offset != null and pageSize != null">
                SELECT
                <include refid="base_column"/>
                FROM (
                SELECT id AS temp_id
                FROM ${tableName} pac
                <include refid="Common_Where_Clause"/>
                <if test="request.orderBy != null and request.orderBy != '' and request.orderDirection != null and request.orderDirection != ''">
                    ORDER BY pac.${request.orderBy} ${request.orderDirection}
                </if>
                <if test="request.orderBy == null or request.orderBy == '' or request.orderDirection == null or request.orderDirection == ''">
                    ORDER BY pac.check_time DESC
                </if>
                LIMIT #{offset}, #{pageSize}
                ) AS t
                INNER JOIN ${tableName} pac ON pac.id = t.temp_id
                <if test="request.orderBy != null and request.orderBy != '' and request.orderDirection != null and request.orderDirection != ''">
                    ORDER BY pac.${request.orderBy} ${request.orderDirection}
                </if>
                <if test="request.orderBy == null or request.orderBy == '' or request.orderDirection == null or request.orderDirection == ''">
                    ORDER BY pac.check_time DESC
                </if>
            </when>
            <!-- 当 offset 或 pageSize 为空时，使用普通的查询模式 -->
            <otherwise>
                SELECT
                <include refid="base_column"/>
                FROM ${tableName} pac
                <include refid="Common_Where_Clause"/>
                <if test="request.orderBy != null and request.orderBy != '' and request.orderDirection != null and request.orderDirection != ''">
                    ORDER BY pac.${request.orderBy} ${request.orderDirection}
                </if>
                <if test="request.orderBy == null or request.orderBy == '' or request.orderDirection == null or request.orderDirection == ''">
                    ORDER BY pac.check_time DESC
                </if>
            </otherwise>
        </choose>
    </select>

    <!-- 查询数据（带子表） -->
    <select id="selectAssetCheckDataWithDetail" resultMap="PatrolAssetCheckDTOMap">
        <bind name="detailTableName" value="detailTableName != null ? detailTableName : tableName + '_detail'"/>
        <choose>
            <!-- 当 offset 和 pageSize 不为空时，使用分页的 INNER JOIN 模式 -->
            <when test="offset != null and pageSize != null">
                SELECT
                pac.id AS id,
                pac.type AS type,
                pac.category AS category,
                pac.kahuna_id AS kahunaId,
                pac.kahuna_name AS kahunaName,
                pac.kahuna_sign AS kahunaSign,
                pac.opr_user_id AS oprUserId,
                pac.opr_user_name AS oprUserName,
                pac.opr_user_sign AS oprUserSign,
                pac.check_time AS checkTime,
                pac.status AS status,
                pac.audit_time AS auditTime,
                pac.image AS image,
                pac.remark AS remark,
                pac.del_flag AS delFlag,
                pac.asset_id AS assetId,
                pac.asset_name AS assetName,
                pac.asset_code AS assetCode,
                pac.route_id AS routeId,
                pac.route_name AS routeName,
                pac.route_code AS routeCode,
                pac.property_unit_id AS propertyUnitId,
                pac.property_unit_name AS propertyUnitName,
                pac.maintain_unit_id AS maintainUnitId,
                pac.maintain_unit_name AS maintainUnitName,
                pac.maintenance_section_id AS maintenanceSectionId,
                pac.maintenance_section_name AS maintenanceSectionName,
                pac.center_stake AS centerStake,
                pac.weather AS weather,
                pac.expiry AS expiry,
                pac.frequency AS frequency,
                pac.stage AS stage,
                pac.disease_count AS diseaseCount,
                pac.create_time AS createTime,
                pac.update_time AS updateTime,
                pac.create_by AS createBy,
                pac.update_by AS updateBy,
                pacd.id AS detail_id,
                pacd.check_id AS checkId,
                pacd.parts_type_id AS partsTypeId,
                pacd.parts_type_name AS partsTypeName,
                pacd.defect AS defect,
                pacd.advice AS advice,
                pacd.image AS detailImage,
                pacd.del_flag AS detailDelFlag,
                pacd.des AS des
                FROM (
                SELECT id AS temp_id
                FROM ${tableName}
                <include refid="Common_Where_Clause"/>
                ORDER BY check_time DESC
                LIMIT #{offset}, #{pageSize}
                ) AS t
                INNER JOIN ${tableName} pac ON pac.id = t.temp_id
                LEFT JOIN ${detailTableName} pacd ON pac.id = pacd.check_id
                ORDER BY pac.check_time DESC, pacd.id ASC
            </when>
            <!-- 当 offset 或 pageSize 为空时，使用普通的查询模式 -->
            <otherwise>
                SELECT
                pac.id AS id,
                pac.type AS type,
                pac.category AS category,
                pac.kahuna_id AS kahunaId,
                pac.kahuna_name AS kahunaName,
                pac.kahuna_sign AS kahunaSign,
                pac.opr_user_id AS oprUserId,
                pac.opr_user_name AS oprUserName,
                pac.opr_user_sign AS oprUserSign,
                pac.check_time AS checkTime,
                pac.status AS status,
                pac.audit_time AS auditTime,
                pac.image AS image,
                pac.remark AS remark,
                pac.del_flag AS delFlag,
                pac.asset_id AS assetId,
                pac.asset_name AS assetName,
                pac.asset_code AS assetCode,
                pac.route_id AS routeId,
                pac.route_name AS routeName,
                pac.route_code AS routeCode,
                pac.property_unit_id AS propertyUnitId,
                pac.property_unit_name AS propertyUnitName,
                pac.maintain_unit_id AS maintainUnitId,
                pac.maintain_unit_name AS maintainUnitName,
                pac.maintenance_section_id AS maintenanceSectionId,
                pac.maintenance_section_name AS maintenanceSectionName,
                pac.center_stake AS centerStake,
                pac.weather AS weather,
                pac.expiry AS expiry,
                pac.frequency AS frequency,
                pac.stage AS stage,
                pac.disease_count AS diseaseCount,
                pac.create_time AS createTime,
                pac.update_time AS updateTime,
                pac.create_by AS createBy,
                pac.update_by AS updateBy,
                pacd.id AS detail_id,
                pacd.check_id AS checkId,
                pacd.parts_type_id AS partsTypeId,
                pacd.parts_type_name AS partsTypeName,
                pacd.defect AS defect,
                pacd.advice AS advice,
                pacd.image AS detailImage,
                pacd.del_flag AS detailDelFlag,
                pacd.des AS des
                FROM ${tableName} pac
                LEFT JOIN ${detailTableName} pacd ON pac.id = pacd.check_id
                <include refid="Common_Where_Clause"/>
                ORDER BY pac.check_time DESC, pacd.id ASC
            </otherwise>
        </choose>
    </select>

    <!-- 查询checkTime > expiry的最大过期时间记录 -->
    <select id="selectMaxExpiryByCheckTimeGreaterThanExpiry" resultMap="PatrolCheckBaseResult">
        WITH FilteredRecords AS (
        SELECT
        asset_id,
        expiry,
        frequency,
        type,
        ROW_NUMBER() OVER (PARTITION BY asset_id ORDER BY expiry DESC) as rn
        FROM
        ${tableName}
        WHERE
        type = #{request.type}
        AND #{request.checkTime} &gt; expiry
        <if test="request.ids != null and request.ids.size() > 0">
            AND asset_id IN
            <foreach collection="request.ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        )
        SELECT
        asset_id,
        expiry,
        frequency,
        type
        FROM
        FilteredRecords
        WHERE
        rn = 1
        ORDER BY
        asset_id
    </select>


    <!-- 结果映射 -->
    <resultMap id="PatrolAssetCheckDTOMap" type="com.ruoyi.patrol.domain.PatrolAssetCheck">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="category" column="category"/>
        <result property="kahunaId" column="kahunaId"/>
        <result property="kahunaName" column="kahunaName"/>
        <result property="kahunaSign" column="kahunaSign"/>
        <result property="oprUserId" column="oprUserId"/>
        <result property="oprUserName" column="oprUserName"/>
        <result property="oprUserSign" column="oprUserSign"/>
        <result property="checkTime" column="checkTime"/>
        <result property="status" column="status"/>
        <result property="auditTime" column="auditTime"/>
        <result property="image" column="image"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="delFlag"/>
        <result property="assetId" column="assetId"/>
        <result property="assetName" column="assetName"/>
        <result property="assetCode" column="assetCode"/>
        <result property="routeId" column="routeId"/>
        <result property="routeName" column="routeName"/>
        <result property="routeCode" column="routeCode"/>
        <result property="propertyUnitId" column="propertyUnitId"/>
        <result property="propertyUnitName" column="propertyUnitName"/>
        <result property="maintainUnitId" column="maintainUnitId"/>
        <result property="maintainUnitName" column="maintainUnitName"/>
        <result property="maintenanceSectionId" column="maintenanceSectionId"/>
        <result property="maintenanceSectionName" column="maintenanceSectionName"/>
        <result property="centerStake" column="centerStake"/>
        <result property="stage" column="stage"/>
        <result property="diseaseCount" column="diseaseCount"/>
        <result property="createTime" column="createTime"/>
        <result property="updateTime" column="updateTime"/>
        <result property="createBy" column="createBy"/>
        <result property="updateBy" column="updateBy"/>
        <collection property="patrolCheckDetailList" ofType="com.ruoyi.patrol.domain.PatrolAssetCheckDetail">
            <id property="id" column="detail_id"/>
            <result property="checkId" column="checkId"/>
            <result property="partsTypeId" column="partsTypeId"/>
            <result property="partsTypeName" column="partsTypeName"/>
            <result property="defect" column="defect"/>
            <result property="advice" column="advice"/>
            <result property="image" column="detailImage"/>
            <result property="delFlag" column="detailDelFlag"/>
            <result property="des" column="des"/>
        </collection>
    </resultMap>

    <!-- 根据创建时间和表名查询病害数大于0的数据 -->
    <select id="selectByCreateTimeAndDiseaseCount" resultMap="PatrolAssetCheckResult">
        SELECT
        <include refid="base_column"/>
        FROM ${tableName}
        WHERE create_time > #{createTime}
        AND disease_count > 0
        AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <update id="updateAuditInfoBatch">
        update ${tableName}
        set status = CASE id
            <foreach collection="list" item="item">
                WHEN #{item.id} THEN #{item.status}
            </foreach>
            END,
            audit_time = CASE id
            <foreach collection="list" item="item">
                WHEN #{item.id} THEN #{item.auditTime}
            </foreach>
            END,
            kahuna_id = CASE id
            <foreach collection="list" item="item">
                WHEN #{item.id} THEN #{item.kahunaId}
            </foreach>
            END,
            kahuna_name = CASE id
            <foreach collection="list" item="item">
                WHEN #{item.id} THEN #{item.kahunaName}
            </foreach>
            END,
            kahuna_sign = CASE id
            <foreach collection="list" item="item">
                WHEN #{item.id} THEN #{item.kahunaSign}
            </foreach>
            END,
            update_time = sysdate()
        where id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>
</mapper>