package com.ruoyi.patrol.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.map.Paramap;
import com.ruoyi.common.datasource.annotation.Master;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.manage.api.domain.RemoteRoadDiseaseResponse;
import com.ruoyi.manage.api.service.RemoteRoadDiseaseService;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolInspectionLogs;
import com.ruoyi.patrol.domain.PatrolInspectionUser;
import com.ruoyi.patrol.domain.cache.BaseDataCache;
import com.ruoyi.patrol.domain.dto.AssetStatsDTO;
import com.ruoyi.patrol.domain.dto.BaseDataDomainWithDistance;
import com.ruoyi.patrol.domain.dto.RouteStatsDTO;
import com.ruoyi.patrol.domain.request.AssetBaseDataRequest;
import com.ruoyi.patrol.domain.vo.*;
import com.ruoyi.patrol.enums.InspectionType;
import com.ruoyi.patrol.mapper.PatrolInspectionLogsMapper;
import com.ruoyi.patrol.service.BaseCacheService;
import com.ruoyi.patrol.service.PatrolInspectionLogsService;
import com.ruoyi.patrol.service.PatrolInspectionUserService;
import com.ruoyi.patroltidb.service.PatrolAssetCheckService;
import com.ruoyi.system.api.RemoteDeptAuthService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteMaintenanceSectionService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.ImageTextRequest;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.dto.BaseMaintenanceSectionDTO;
import com.ruoyi.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.convert.Convert;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ruoyi.common.security.utils.SecurityUtils.getLoginUser;

/**
 * 巡查日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Service
@Master
public class PatrolInspectionLogsServiceImpl extends ServiceImpl<PatrolInspectionLogsMapper, PatrolInspectionLogs> implements PatrolInspectionLogsService {

    private static final Logger log = LoggerFactory.getLogger(PatrolInspectionLogsServiceImpl.class);

    @Resource
    PatrolInspectionLogsMapper patrolInspectionLogsMapper;
    @Resource
    RemoteMaintenanceSectionService remoteMaintenanceSectionService;
    @Resource
    RemoteUserService remoteUserService;
    @Resource
    RemoteDeptAuthService remoteDeptAuthService;
    @Resource
    PatrolInspectionUserService patrolInspectionUserService;
    @Resource
    BaseCacheService baseCacheService;
    @Resource
    RemoteRoadDiseaseService remoteRoadDiseaseService;
    @Resource
    private RemoteFileService remoteFileService;

    @Resource
    @Lazy
    PatrolAssetCheckService patrolAssetCheckService;

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;


    @Override
    public List<PatrolInspectionLogs> findListByParam(Map<String, Object> params) {
        return patrolInspectionLogsMapper.findListByUser(params);
    }

    /**
     * 统计用户巡查记录数量
     *
     * @param params 查询参数
     * @return 巡查记录数量
     */
    @Override
    public Integer countListByUser(Map<String, Object> params) {
        return patrolInspectionLogsMapper.countListByUser(params);
    }

    /**
     * 回显设置,巡查路段名称和巡查人员名称等等未来的其他的
     */
    public void setOtherInfo(List<PatrolInspectionLogs> list) {
        //巡查路段回显
        this.setMaintenanceSectionName(list);
        //巡查人员回显
        this.setUserNames(list);
    }

    /**
     * 根据id查询
     *
     * @param id id
     * @return PatrolInspectionLogs
     */
    @Override
    public PatrolInspectionLogs selectById(String id) {
        PatrolInspectionLogs patrolInspectionLogs = patrolInspectionLogsMapper.selectPatrolInspectionLogsById(id);
        this.setUserNames(Collections.singletonList(patrolInspectionLogs));
        return patrolInspectionLogs;
    }

    /**
     * 寻找时间区间内各个路段最新的一条巡查记录
     *
     * @param lastTime      最近生成巡查记录的时间
     * @param nowTime       当前时间
     * @param sectionIdList 路段id列表
     * @return List<PatrolInspectionLogs>
     */
    @Override
    public List<PatrolInspectionLogs> findLastOneByTime(LocalDate lastTime, LocalDate nowTime, List<String> sectionIdList) {
        List<PatrolInspectionLogs> list =
                patrolInspectionLogsMapper.selectLatestPatrolInspectionLogs(lastTime, nowTime, sectionIdList);
        this.setUserNames(list);
        return list;
    }

    /**
     * 获取当前用户的巡查记录，时间区间内的 preTime<= checkTime < endTime
     *
     * @param sectionIds 养护路段id列表
     * @param preTime    开始时间
     * @param endTime    结束时间
     */
    @Override
    public List<PatrolInspectionLogs> findListByUserAndTime(List<String> sectionIds, LocalDateTime preTime, LocalDateTime endTime, Integer nowTime) {
        return patrolInspectionLogsMapper.findListByUserAndTime(sectionIds, preTime, endTime, nowTime);
    }

    @Override
    public List<Integer> getDaysWithData(String maintenanceSectionId, String yearMonth) {
        return patrolInspectionLogsMapper.findDaysWithDataBySectionAndMonth(maintenanceSectionId, yearMonth);
    }

    /**
     * 根据巡查记录id获取巡查事件
     *
     * @param recordId 巡查记录id
     * @return 巡查事件列表
     */
    @Override
    public List<RemoteRoadDiseaseResponse> getRoadDiseaseList(String recordId) {
        R<List<RemoteRoadDiseaseResponse>> r = remoteRoadDiseaseService.getRoadDiseaseList(recordId);
        if (r == null) {
            throw new RuntimeException("获取巡查事件失败:远程调用失败");
        }
        if (r.getCode() != 200) {
            throw new RuntimeException("获取巡查事件失败:" + r.getMsg());
        }
        return r.getData();
    }


    /**
     * 巡查路段回显
     */
    private void setMaintenanceSectionName(List<PatrolInspectionLogs> list) {
        R<List<BaseMaintenanceSectionDTO>> listAll = remoteMaintenanceSectionService.listAll();
        for (PatrolInspectionLogs logs : list) {
            for (BaseMaintenanceSectionDTO datum : listAll.getData()) {
                if (datum.getMaintenanceSectionId().equals(logs.getMaintenanceSectionId())) {
                    logs.setMaintenanceSectionName(datum.getMaintenanceSectionName());
                    break;
                }
            }
        }
    }

    /**
     * 巡查人员回显
     */
    private void setUserNames(List<PatrolInspectionLogs> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        Set<Long> userIdList = new HashSet<>();
        for (PatrolInspectionLogs logs : list) {
            if (!StrUtil.isBlank(logs.getUserIdStr())) {
                for (String str : logs.getUserIdStr().split(",")) {
                    userIdList.add(Long.valueOf(str));
                }
            }
        }
        R<List<SysUser>> rParam = remoteUserService.findListParam(userIdList.stream().toList());
        final List<SysUser> data = rParam.getData();
        if (data != null && !data.isEmpty()) {
            for (PatrolInspectionLogs logs : list) {
                StringBuilder userNames = new StringBuilder();
                StringBuilder signIds = new StringBuilder();
                StringBuilder nickNames = new StringBuilder();
                for (SysUser user : data) {
                    if (!StrUtil.isBlank(logs.getUserIdStr()) && logs.getUserIdStr().contains(user.getUserId().toString())) {
                        if (!StrUtil.isBlank(userNames)) {
                            userNames.append(",");
                        }
                        userNames.append(user.getUserName());
                        if (!StrUtil.isBlank(signIds)) {
                            signIds.append(",");
                        }
                        signIds.append(user.getSignId());
                        if (!StrUtil.isBlank(nickNames)) {
                            nickNames.append(",");
                        }
                        nickNames.append(user.getNickName());
                    }
                }
                logs.setUserNames(userNames.toString());
                logs.setSignIds(signIds.toString());
                logs.setNickNames(nickNames.toString());
                logs.setNickNameList(Arrays.asList(nickNames.toString().split(",")));
            }
        }
    }

    /**
     * 查询指定养护路段和日期内有数据的小时列表
     *
     * @param sectionId 养护路段ID
     * @param lastDate  日期
     * @param nowDate   日期
     * @return 小时列表
     */
    @Override
    public PatrolInspectionLogs findLatestBySectionIdAndTime(String sectionId, LocalDate lastDate, LocalDate nowDate) {
        List<LocalDate> dates = new ArrayList<>();
        LocalDate currentDate = lastDate.plusDays(1);  // 从lastDate后一天开始
        while (!currentDate.isAfter(nowDate)) {  // 直到nowDate为止
            dates.add(currentDate);
            currentDate = currentDate.plusDays(1);
        }
        LocalDate time = dates.get(0);
        PatrolInspectionLogs latest = patrolInspectionLogsMapper.findLatestBySectionIdAndTime(sectionId, time);
        if (latest != null) {
            this.setUserNames(Collections.singletonList(latest));
            latest.setId(IdWorker.getIdStr());
            latest.setCreateTime(null);
            latest.setUpdateTime(null);
            latest.setCreateBy(null);
            latest.setUpdateBy(null);
            // 巡查开始时间
            latest.setStartTime(Date.from(time.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
            // 巡查结束时间
            latest.setEndTime(Date.from(time.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant()));
            // 设置上报时间为当天开始
            latest.setReportedTime(Date.from(time.atTime(12, 0, 0).atZone(ZoneId.systemDefault()).toInstant()));
            // 设置收集时间为当天开始
            latest.setCollectTime(Date.from(time.atTime(12, 0, 0).atZone(ZoneId.systemDefault()).toInstant()));
            // 设置允许的时间
            latest.setDateList(dates);
            // 设置巡查状态为完成
            latest.setStatus(1);
            latest.setRemark("系统自动生成");
            return latest;
        } else {
            BaseMaintenanceSectionDTO section = remoteMaintenanceSectionService.getInfo(sectionId).getData();
            SysDept dept = remoteDeptAuthService.getById(Long.valueOf(section.getDepartmentId())).getData();
            PatrolInspectionLogs log = new PatrolInspectionLogs();
            log.setId(IdWorker.getIdStr()); // 生成唯一ID
            // 巡查类型 日间巡查
            log.setPatrolType(1);
            // 设置路段ID和路段名称
            log.setMaintenanceSectionId(sectionId);
            log.setMaintenanceSectionName(section.getMaintenanceSectionName());
            // 设置管养单位ID和管养单位名称
            log.setMaintenanceUnitId(section.getDepartmentId());
            if (dept != null) {
                log.setMaintenanceUnitName(dept.getDeptName());
            }
            // 巡查方向 双向
            log.setDirection(3);
            // 车牌
            log.setCarNum("云A12345");
            // 天气
            log.setWeather("晴");
            // 巡查内容
            log.setContent("对路面、桥梁、隧道、路基、绿化、交通工程等进行日常巡查");
            // 巡查开始时间
            log.setStartTime(Date.from(time.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
            // 巡查结束时间
            log.setEndTime(Date.from(time.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant()));
            // 设置上报时间为当天开始
            log.setReportedTime(Date.from(time.atTime(12, 0, 0).atZone(ZoneId.systemDefault()).toInstant()));
            // 设置收集时间为当天开始
            log.setCollectTime(Date.from(time.atTime(12, 0, 0).atZone(ZoneId.systemDefault()).toInstant()));
            // 设置允许的时间
            log.setDateList(dates);
            // 设置巡查状态为完成
            log.setStatus(1);
            // 设置巡查里程为 String 转 BigDecimal
            log.setPatrolMileage(new BigDecimal(section.getRoadSectionLength()));
            log.setRemark("系统自动生成");
            return log;
        }
    }

    @Override
    public InspectionStatsVO patrolDetail(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 验证用户登录
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null) {
                throw new RuntimeException("获取当前用户失败");
            }

            // 构建桥梁请求
            AssetBaseDataRequest bridgeRequest = AssetBaseDataRequest.builder()
                    .type(InspectionType.BRIDGE_DAILY_INSPECTION)
                    .checkTime(LocalDateTime.now())
                    .dataRule(true)
                    .build();
            baseCacheService.setDeptIds(bridgeRequest);
            bridgeRequest.setDataRule(false);

            // 构建隧道请求
            AssetBaseDataRequest tunnelRequest = AssetBaseDataRequest.builder()
                    .type(InspectionType.TUNNEL_DAILY_INSPECTION)
                    .checkTime(LocalDateTime.now())
                    .dataRule(true)
                    .build();
            baseCacheService.setDeptIds(tunnelRequest);
            tunnelRequest.setDataRule(false);

            // 顺序执行统计计算
            RouteStatsDTO routeStats;
            try {
                routeStats = calculateRouteStats(startTime, endTime, loginUser.getUserid(), null);
            } catch (Exception e) {
                log.error("计算路线统计数据失败", e);
                routeStats = createEmptyRouteStats();
            }

            AssetStatsDTO bridgeStats;
            try {
                bridgeStats = calculateAssetStats(bridgeRequest);
            } catch (Exception e) {
                log.error("计算桥梁统计数据失败", e);
                bridgeStats = createEmptyAssetStats();
            }

            AssetStatsDTO tunnelStats;
            try {
                tunnelStats = calculateAssetStats(tunnelRequest);
            } catch (Exception e) {
                log.error("计算隧道统计数据失败", e);
                tunnelStats = createEmptyAssetStats();
            }

            // 组装并返回结果
            return assembleInspectionStatsVO(routeStats, bridgeStats, tunnelStats, null, null);

        } catch (Exception e) {
            log.error("巡查统计数据处理失败", e);
            // 返回空结果而不是抛出异常
            return assembleEmptyInspectionStatsVO();
        }
    }

    /**
     * 新版月度巡查详情（路面巡查计本日，桥隧涵计本月）
     */
    public InspectionStatsVO patrolDetailNew() {
        try {
            // 验证用户登录
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser == null) {
                throw new RuntimeException("获取当前用户失败");
            }
            // 顺序执行统计计算
            RouteStatsDTO routeStats;
            try {
                //计算当天
                routeStats = calculateRouteStats(null, null, loginUser.getUserid(), 1);
            } catch (Exception e) {
                log.error("计算路线统计数据失败", e);
                routeStats = createEmptyRouteStats();
            }

            AssetStatsDTO bridgeStats;
            try {
                // 构建桥梁请求
                AssetBaseDataRequest bridgeRequest = AssetBaseDataRequest.builder()
                        .type(InspectionType.BRIDGE_REGULAR_INSPECTION)
                        .checkTime(LocalDateTime.now())
                        .dataRule(true)
                        .build();
                baseCacheService.setDeptIds(bridgeRequest);
                bridgeRequest.setDataRule(false);
                bridgeStats = calculateAssetStats(bridgeRequest);
            } catch (Exception e) {
                log.error("计算桥梁统计数据失败", e);
                bridgeStats = createEmptyAssetStats();
            }

            AssetStatsDTO tunnelStats;
            try {
                // 构建隧道请求
                AssetBaseDataRequest tunnelRequest = AssetBaseDataRequest.builder()
                        .type(InspectionType.TUNNEL_REGULAR_INSPECTION)
                        .checkTime(LocalDateTime.now())
                        .dataRule(true)
                        .build();
                baseCacheService.setDeptIds(tunnelRequest);
                tunnelRequest.setDataRule(false);
                tunnelStats = calculateAssetStats(tunnelRequest);
            } catch (Exception e) {
                log.error("计算隧道统计数据失败", e);
                tunnelStats = createEmptyAssetStats();
            }

            AssetStatsDTO culvertStats;
            try {
                // 构建涵洞请求
                AssetBaseDataRequest culvertRequest = AssetBaseDataRequest.builder()
                        .type(InspectionType.CULVERT_REGULAR_INSPECTION)
                        .checkTime(LocalDateTime.now())
                        .dataRule(true)
                        .build();
                baseCacheService.setDeptIds(culvertRequest);
                culvertRequest.setDataRule(false);
                culvertStats = calculateAssetStats(culvertRequest);
            } catch (Exception e) {
                log.error("计算涵洞统计数据失败", e);
                culvertStats = createEmptyAssetStats();
            }

            AssetStatsDTO deviceStats;
            try {
                // 构建隧道机电请求
                AssetBaseDataRequest deviceRequest = AssetBaseDataRequest.builder()
                        .type(InspectionType.DEVICE_REGULAR_INSPECTION)
                        .checkTime(LocalDateTime.now())
                        .dataRule(true)
                        .build();
                baseCacheService.setDeptIds(deviceRequest);
                deviceRequest.setDataRule(false);
                deviceStats = calculateAssetStats(deviceRequest);
            } catch (Exception e) {
                log.error("计算机电统计数据失败", e);
                deviceStats = createEmptyAssetStats();
            }

            // 组装并返回结果
            return assembleInspectionStatsVO(routeStats, bridgeStats, tunnelStats, culvertStats, deviceStats);

        } catch (Exception e) {
            log.error("巡查统计数据处理失败", e);
            // 返回空结果而不是抛出异常
            return assembleEmptyInspectionStatsVO();
        }
    }

    // 计算路段统计数据
    private RouteStatsDTO calculateRouteStats(LocalDateTime startTime, LocalDateTime endTime, Long userId, Integer nowTime) {
        List<BaseMaintenanceSectionDTO> sections = remoteMaintenanceSectionService.findUserMaintenanceList(userId).getData();

        if (sections == null || sections.isEmpty()) {
            return RouteStatsDTO.builder()
                    .totalLength(BigDecimal.ZERO)
                    .inspectedLength(BigDecimal.ZERO)
                    .inspectedCount(0)
                    .pendingCount(0)
                    .build();
        }

        BigDecimal totalLength = sections.stream()
                .map(BaseMaintenanceSectionDTO::getMainLength)
                .filter(length -> length != null && !length.trim().isEmpty())
                .map(length -> {
                    try {
                        return new BigDecimal(length.trim());
                    } catch (NumberFormatException e) {
                        return BigDecimal.ZERO;
                    }
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        List<String> sectionIds = sections.stream()
                .map(BaseMaintenanceSectionDTO::getMaintenanceSectionId)
                .collect(Collectors.toList());

        List<PatrolInspectionLogs> logs = findListByUserAndTime(sectionIds, startTime, endTime, nowTime);
        // logs根据maintenanceSectionId去重 保留最新的一条
        List<PatrolInspectionLogs> distinctLogs = logs.stream()
                .collect(Collectors.toMap(PatrolInspectionLogs::getMaintenanceSectionId, log -> log, (log1, log2) -> log1))
                .values()
                .stream()
                .collect(Collectors.toList());

        BigDecimal inspectedLength = distinctLogs.stream()
                .map(PatrolInspectionLogs::getPatrolMileage)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return RouteStatsDTO.builder()
                .totalLength(totalLength.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP))
                .inspectedLength(inspectedLength.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP))
                .inspectedCount(distinctLogs.size())
                .pendingCount(sections.size() - distinctLogs.size())
                .build();
    }


    /**
     * 新版月度巡查详情（路面巡查计本日，桥隧涵计本月） 不要权限
     */
    public InspectionStatsVO patrolDetailNew(String managementMaintenanceId, Integer year, Integer month) {
        try {
            // 获取当前月份的第一天
            LocalDateTime startTime = LocalDateTime.of(year, month, 1, 0, 0, 0);
            // 下个月的第一天
            LocalDateTime endTime = startTime.plusMonths(1).minusDays(1);
            // 构建桥梁请求
            AssetBaseDataRequest bridgeRequest = AssetBaseDataRequest.builder()
                    .type(InspectionType.BRIDGE_REGULAR_INSPECTION)
                    .checkStartTime(startTime)
                    .checkEndTime(endTime)
                    .dataRule(false)
                    .managementMaintenanceId(managementMaintenanceId)
                    .build();
            baseCacheService.setDeptIds(bridgeRequest);

            // 构建隧道请求
            AssetBaseDataRequest tunnelRequest = AssetBaseDataRequest.builder()
                    .type(InspectionType.TUNNEL_REGULAR_INSPECTION)
                    .checkStartTime(startTime)
                    .checkEndTime(endTime)
                    .dataRule(false)
                    .managementMaintenanceId(managementMaintenanceId)
                    .build();
            baseCacheService.setDeptIds(tunnelRequest);

            // 构建涵洞请求
            AssetBaseDataRequest culvertRequest = AssetBaseDataRequest.builder()
                    .type(InspectionType.CULVERT_REGULAR_INSPECTION)
                    .checkStartTime(startTime)
                    .checkEndTime(endTime)
                    .dataRule(false)
                    .managementMaintenanceId(managementMaintenanceId)
                    .build();
            baseCacheService.setDeptIds(culvertRequest);

            // 构建隧道机电请求
            AssetBaseDataRequest deviceRequest = AssetBaseDataRequest.builder()
                    .type(InspectionType.DEVICE_REGULAR_INSPECTION)
                    .checkStartTime(startTime)
                    .checkEndTime(endTime)
                    .dataRule(false)
                    .managementMaintenanceId(managementMaintenanceId)
                    .build();
            baseCacheService.setDeptIds(deviceRequest);

            // 顺序执行统计计算
            RouteStatsDTO routeStats;
            try {
                routeStats = calculateRouteStats(startTime, endTime, managementMaintenanceId);
            } catch (Exception e) {
                log.error("计算路线统计数据失败", e);
                routeStats = createEmptyRouteStats();
            }

            AssetStatsDTO bridgeStats;
            try {
                bridgeStats = calculateAssetStats(bridgeRequest);
            } catch (Exception e) {
                log.error("计算桥梁统计数据失败", e);
                bridgeStats = createEmptyAssetStats();
            }

            AssetStatsDTO tunnelStats;
            try {
                tunnelStats = calculateAssetStats(tunnelRequest);
            } catch (Exception e) {
                log.error("计算隧道统计数据失败", e);
                tunnelStats = createEmptyAssetStats();
            }

            AssetStatsDTO culvertStats;
            try {
                culvertStats = calculateAssetStats(culvertRequest);
            } catch (Exception e) {
                log.error("计算涵洞统计数据失败", e);
                culvertStats = createEmptyAssetStats();
            }

            AssetStatsDTO deviceStats;
            try {
                deviceStats = calculateAssetStats(deviceRequest);
            } catch (Exception e) {
                log.error("计算机电统计数据失败", e);
                deviceStats = createEmptyAssetStats();
            }

            // 组装并返回结果
            return assembleInspectionStatsVO(routeStats, bridgeStats, tunnelStats, culvertStats, deviceStats);

        } catch (Exception e) {
            log.error("巡查统计数据处理失败", e);
            // 返回空结果而不是抛出异常
            return assembleEmptyInspectionStatsVO();
        }
    }

    // 计算路段统计数据 （不要权限）
    private RouteStatsDTO calculateRouteStats(LocalDateTime startTime, LocalDateTime endTime, String managementMaintenanceId) {
        List<BaseMaintenanceSectionDTO> sections = remoteMaintenanceSectionService.getByDeptIdMSList(Long.valueOf(managementMaintenanceId)).getData();

        if (sections == null || sections.isEmpty()) {
            return RouteStatsDTO.builder()
                    .totalLength(BigDecimal.ZERO)
                    .inspectedLength(BigDecimal.ZERO)
                    .inspectedCount(0)
                    .pendingCount(0)
                    .build();
        }

        BigDecimal totalLength = sections.stream()
                .map(BaseMaintenanceSectionDTO::getMainLength)
                .filter(length -> length != null && !length.trim().isEmpty())
                .map(length -> {
                    try {
                        return new BigDecimal(length.trim());
                    } catch (NumberFormatException e) {
                        return BigDecimal.ZERO;
                    }
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        List<String> sectionIds = sections.stream()
                .map(BaseMaintenanceSectionDTO::getMaintenanceSectionId)
                .collect(Collectors.toList());

        List<PatrolInspectionLogs> logs = findListByUserAndTime(sectionIds, startTime, endTime, null);
//        // logs根据maintenanceSectionId去重 保留最新的一条
        List<PatrolInspectionLogs> distinctLogs = logs.stream()
                .collect(Collectors.toMap(PatrolInspectionLogs::getMaintenanceSectionId, log -> log, (log1, log2) -> log1))
                .values()
                .stream()
                .collect(Collectors.toList());

        BigDecimal inspectedLength = logs.stream()
                .map(PatrolInspectionLogs::getPatrolMileage)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return RouteStatsDTO.builder()
                .totalLength(totalLength.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP))
                .inspectedLength(inspectedLength.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP))
                .inspectedCount(logs.size())
                .pendingCount(sections.size() - distinctLogs.size())
                .totalCount(sections.size())
                .build();
    }

    // 计算资产统计数据
    private AssetStatsDTO calculateAssetStats(AssetBaseDataRequest request) {

        List<BaseDataDomainWithDistance<BaseDataCache>> assets =
                baseCacheService.listBy(request, null, null, new AtomicInteger());

        long inspectedCount = assets.stream()
                .filter(asset -> asset.getDistance() != null || asset.getIsCheck())
                .count();

        return AssetStatsDTO.builder()
                .totalCount(assets.size())
                .inspectedCount((int) inspectedCount)
                .pendingCount((int) (assets.size() - inspectedCount))
                .build();
    }

    // 组装最终返回结果
    private InspectionStatsVO assembleInspectionStatsVO(RouteStatsDTO routeStats,
                                                        AssetStatsDTO bridgeStats, AssetStatsDTO tunnelStats, AssetStatsDTO culvertStats, AssetStatsDTO deviceStats) {
        // 为null的情况创建空对象
        routeStats = routeStats != null ? routeStats : createEmptyRouteStats();
        bridgeStats = bridgeStats != null ? bridgeStats : createEmptyAssetStats();
        tunnelStats = tunnelStats != null ? tunnelStats : createEmptyAssetStats();
        culvertStats = culvertStats != null ? culvertStats : createEmptyAssetStats();
        deviceStats = deviceStats != null ? deviceStats : createEmptyAssetStats();
        return InspectionStatsVO.builder()
                // 路段相关数据
                .totalMileage(routeStats.getTotalLength().toString()) // 总里程
                .inspectedMileage(routeStats.getInspectedLength().toString()) // 已巡查里程
                .pendingMileage(routeStats.getTotalLength().subtract(routeStats.getInspectedLength()).toString()) // 待巡查里程
                .routeInspectedCount(routeStats.getInspectedCount()) // 已巡查路段数
                .routePendingCount(routeStats.getPendingCount()) // 待巡查路段数
                .routeInspections(formatInspectionCount(
                        routeStats.getInspectedCount(), routeStats.getPendingCount())) // 路段巡查

                // 桥梁相关数据
                .bridgeTotalCount(bridgeStats.getTotalCount()) // 计划巡查桥梁数
                .bridgeInspectedCount(bridgeStats.getInspectedCount()) // 已巡查桥梁数
                .bridgePendingCount(bridgeStats.getPendingCount()) // 待巡查桥梁数
                .bridgeInspections(formatInspectionCount(
                        bridgeStats.getInspectedCount(), bridgeStats.getPendingCount())) // 桥梁巡查

                // 涵洞相关数据
                .culvertTotalCount(culvertStats.getTotalCount()) // 计划巡查桥梁数
                .culvertInspectedCount(culvertStats.getInspectedCount()) // 已巡查桥梁数
                .culvertPendingCount(culvertStats.getPendingCount()) // 待巡查桥梁数
                .culvertInspections(formatInspectionCount(
                        culvertStats.getInspectedCount(), culvertStats.getPendingCount())) // 桥梁巡查

                // 隧道相关数据
                .tunnelTotalCount(tunnelStats.getTotalCount()) // 计划巡查隧道数
                .tunnelInspectedCount(tunnelStats.getInspectedCount()) // 已巡查隧道数
                .tunnelPendingCount(tunnelStats.getPendingCount()) // 待巡查隧道数
                .tunnelInspections(formatInspectionCount(
                        tunnelStats.getInspectedCount(), tunnelStats.getPendingCount())) // 隧道巡查

                // 隧道机电相关
                .tunnelDeviceTotalCount(deviceStats.getTotalCount()) // 计划巡查隧道数
                .tunnelDeviceInspectedCount(deviceStats.getInspectedCount()) // 已巡查隧道数

                .build();
    }

    private String formatInspectionCount(int inspected, int pending) {
        int total = inspected + pending;
        return inspected + "/" + total;
    }

    //    @Override
    public List<InspectionStatsVO> patrolDetailByDepartments(LocalDateTime startTime, LocalDateTime endTime) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            throw new RuntimeException("获取当前用户失败");
        }

        // 1. 配置资产请求
        AssetBaseDataRequest bridgeRequest = AssetBaseDataRequest.builder()
                .type(InspectionType.BRIDGE_DAILY_INSPECTION)
                .checkTime(LocalDateTime.now())
                .dataRule(true)
                .build();
        baseCacheService.setDeptIds(bridgeRequest);
        bridgeRequest.setDataRule(false);

        AssetBaseDataRequest tunnelRequest = AssetBaseDataRequest.builder()
                .type(InspectionType.TUNNEL_DAILY_INSPECTION)
                .checkTime(LocalDateTime.now())
                .dataRule(true)
                .build();
        baseCacheService.setDeptIds(tunnelRequest);
        tunnelRequest.setDataRule(false);

        // 2. 创建三个异步任务
        CompletableFuture<Map<String, RouteStatsDTO>> routeStatsFuture = CompletableFuture.supplyAsync(() ->
                calculateRouteStatsByDepartment(startTime, endTime, loginUser.getUserid()));

        CompletableFuture<Map<String, AssetStatsDTO>> bridgeStatsFuture = CompletableFuture.supplyAsync(() ->
                calculateAssetStatsByDepartment(bridgeRequest));

        CompletableFuture<Map<String, AssetStatsDTO>> tunnelStatsFuture = CompletableFuture.supplyAsync(() ->
                calculateAssetStatsByDepartment(tunnelRequest));

        try {
            CompletableFuture.allOf(routeStatsFuture, bridgeStatsFuture, tunnelStatsFuture).join();

            Map<String, RouteStatsDTO> routeStatsMap = routeStatsFuture.get();
            Map<String, AssetStatsDTO> bridgeStatsMap = bridgeStatsFuture.get();
            Map<String, AssetStatsDTO> tunnelStatsMap = tunnelStatsFuture.get();

            // 3. 收集所有部门ID
            // 使用 Stream 并行处理
            Set<String> allDepartmentIds = Stream.of(
                            routeStatsMap.keySet(),
                            bridgeStatsMap.keySet(),
                            tunnelStatsMap.keySet()
                    )
                    .flatMap(Set::stream)
                    .filter(id -> id != null && !id.isEmpty())
                    .collect(Collectors.toSet());

            // 4. 获取部门名称
            Map<String, String> deptNames = getDepartmentNames(allDepartmentIds);

            // 5. 组装结果
            // 修改 patrolDetailByDepartments 方法的返回部分:
            return allDepartmentIds.parallelStream()
                    .map(deptId -> {
                        RouteStatsDTO routeStats = routeStatsMap.getOrDefault(deptId, createEmptyRouteStats());
                        AssetStatsDTO bridgeStats = bridgeStatsMap.getOrDefault(deptId, createEmptyAssetStats());
                        AssetStatsDTO tunnelStats = tunnelStatsMap.getOrDefault(deptId, createEmptyAssetStats());

                        return assembleInspectionStatsVO(routeStats, bridgeStats, tunnelStats, null, null)
                                .toBuilder()
                                .managementMaintenanceId(deptId)
                                .managementMaintenanceName(deptNames.getOrDefault(deptId, "未知"))
                                .build();
                    })
                    .sorted(Comparator
                            .comparing(InspectionStatsVO::getTotalInspectedCount).reversed()
                            .thenComparing(InspectionStatsVO::getTotalCount).reversed()
                    )
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("计算部门巡查统计数据失败", e);
            throw new RuntimeException("计算部门巡查统计数据失败", e);
        }
    }

    // 按部门统计路段数据
    private Map<String, RouteStatsDTO> calculateRouteStatsByDepartment(LocalDateTime startTime, LocalDateTime endTime, Long userId) {
        List<BaseMaintenanceSectionDTO> sections = remoteMaintenanceSectionService.findUserMaintenanceList(userId).getData();
        if (sections == null || sections.isEmpty()) {
            return Collections.emptyMap();
        }

        return sections.stream()
                .collect(Collectors.groupingBy(
                        BaseMaintenanceSectionDTO::getDepartmentId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                sectionList -> {
                                    BigDecimal totalLength = sectionList.stream()
                                            .map(BaseMaintenanceSectionDTO::getMainLength)
                                            .filter(length -> length != null && !length.trim().isEmpty())
                                            .map(BigDecimal::new)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                                    List<String> sectionIds = sectionList.stream()
                                            .map(BaseMaintenanceSectionDTO::getMaintenanceSectionId)
                                            .collect(Collectors.toList());

                                    List<PatrolInspectionLogs> logs = findListByUserAndTime(sectionIds, startTime, endTime, null);

                                    BigDecimal inspectedLength = logs.stream()
                                            .map(PatrolInspectionLogs::getPatrolMileage)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                                    return RouteStatsDTO.builder()
                                            .totalLength(totalLength)
                                            .inspectedLength(inspectedLength)
                                            .inspectedCount(logs.size())
                                            .pendingCount(sectionList.size() - logs.size())
                                            .build();
                                }
                        )
                ));
    }

    private Map<String, AssetStatsDTO> calculateAssetStatsByDepartment(AssetBaseDataRequest request) {
        List<BaseDataDomainWithDistance<BaseDataCache>> assets =
                baseCacheService.listBy(request, null, null, new AtomicInteger());

        return assets.stream()
                .filter(asset -> asset != null && asset.getBaseData() != null)
                .filter(asset -> asset.getBaseData().getManagementMaintenanceId() != null)
                .collect(Collectors.groupingBy(
                        asset -> asset.getBaseData().getManagementMaintenanceId(),  // 直接获取ID，因为已经确保不为null
//                        asset -> Optional.ofNullable(asset.getBaseData().getManagementMaintenanceId())
//                                .orElse("0"),  // 将null值映射为"未知"
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                assetList -> {
                                    long inspectedCount = assetList.stream()
                                            .filter(BaseDataDomainWithDistance::getIsCheck)
                                            .count();
                                    return AssetStatsDTO.builder()
                                            .inspectedCount((int) inspectedCount)
                                            .pendingCount((int) (assetList.size() - inspectedCount))
                                            .build();
                                }
                        )
                ));
    }

    /**
     * 获取当前条件下的养护里程总和
     *
     * @param request 查询条件
     * @return 养护里程总和
     */
    @Override
    public BigDecimal getTotalPatrolMileage(AssetBaseDataRequest request) {
        return patrolInspectionLogsMapper.getTotalPatrolMileage(request);
    }

    /**
     * 权限id设置
     *
     * @param request 资产请求对象
     */
    @Override
    public void setDeptIds(AssetBaseDataRequest request) {
        // 获取数据规则标志，如果为空则默认为true
        boolean dataRule = Optional.ofNullable(request.getDataRule())
                .orElse(true);

        // 如果是管理员或不需要数据规则，直接返回
        if (Optional.ofNullable(getLoginUser())
                .map(LoginUser::getSysUser)
                .map(SysUser::isAdmin)
                .orElse(false) || !dataRule) {
            request.processIdAndExclude();
            return;
        }

//        // 获取资产类型
//        AssetType assetType = Optional.ofNullable(request.getAssetType())
//                .orElseGet(() -> Optional.ofNullable(request.getType())
//                        .map(InspectionType::getAssetType)
//                        .orElse(null));
//
//        if (assetType == null) {
//            request.processIdAndExclude();
//            return;
//        }
        Set<String> routeIds = Optional.ofNullable(remoteMaintenanceSectionService.findUserMaintenanceList(getLoginUser().getUserid()))
                .map(R::getData)
                .map(sections -> sections.stream()
                        .map(BaseMaintenanceSectionDTO::getMaintenanceSectionId)
                        .collect(Collectors.toSet()))
                .orElseGet(HashSet::new);

        // 如果有权限集合且请求中包含IDs，取交集
        if (routeIds != null && request.getIds() != null && !request.getIds().isEmpty()) {
            routeIds.retainAll(request.getIds());
        }

        // 设置处理后的IDs
        request.setIds(Optional.ofNullable(routeIds)
                .map(ArrayList::new)
                .orElseGet(ArrayList::new));
        if (request.getIds() != null && request.getIds().isEmpty()) {
            throw new RuntimeException("无养护路段资产权限查看数据");
        }
        request.processIdAndExclude();
    }

    // 获取部门名称Map
    private Map<String, String> getDepartmentNames(Set<String> departmentIds) {
        return remoteDeptAuthService.getByDeptIdList(departmentIds.stream()
                        .map(Long::valueOf)
                        .collect(Collectors.toList()))
                .getData()
                .stream()
                .collect(Collectors.toMap(
                        dept -> dept.getDeptId().toString(),
                        SysDept::getDeptName,
                        (a, b) -> a
                ));
    }

    // 创建空的统计对象
    private RouteStatsDTO createEmptyRouteStats() {
        return RouteStatsDTO.builder()
                .totalLength(BigDecimal.ZERO)
                .inspectedLength(BigDecimal.ZERO)
                .inspectedCount(0)
                .pendingCount(0)
                .build();
    }

    private AssetStatsDTO createEmptyAssetStats() {
        return AssetStatsDTO.builder()
                .inspectedCount(0)
                .pendingCount(0)
                .build();
    }

    /**
     * 批量插入巡查记录
     *
     * @param logsList 巡查记录列表
     */
    @Override
    public int insertBatch(List<PatrolInspectionLogs> logsList) {
        if (logsList == null || logsList.isEmpty()) {
            return 0;
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            return 0;
        }
        SysDept dept = loginUser.getSysUser().getDept();
        Random random = new Random();
        for (PatrolInspectionLogs logs : logsList) {
            //添加巡查机构
            logs.setPatrolUnitId(dept.getDeptId());
            logs.setPatrolUnitName(dept.getDeptName());

            // 随机化巡查人员
            List<Long> originalUserIds = logs.getUserIds();
            if (CollectionUtil.isNotEmpty(originalUserIds)) {
                // 随机决定人数 (1 到 原始人数)
                int numberOfUsers = random.nextInt(originalUserIds.size()) + 1;
                // 打乱列表并选取
                List<Long> shuffledUserIds = new ArrayList<>(originalUserIds);
                Collections.shuffle(shuffledUserIds, random);
                logs.setUserIds(new ArrayList<>(shuffledUserIds.subList(0, numberOfUsers)));
            }

            // 随机化时间
            Date originalCollectTime = logs.getCollectTime();
            if (originalCollectTime == null) {
                originalCollectTime = new Date(); // 如果为空，则使用当前时间
            }
            LocalDateTime collectTime = originalCollectTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

            // 随机化 collectTime，前后30分钟
            int collectTimeOffset = random.nextInt(61) - 30; // -30 to +30
            LocalDateTime newCollectTime = collectTime.plusMinutes(collectTimeOffset);
            logs.setCollectTime(Date.from(newCollectTime.atZone(ZoneId.systemDefault()).toInstant()));

            // reportedTime 等于随机后的 collectTime
            logs.setReportedTime(logs.getCollectTime());

            // 随机化 startTime 和 endTime，时长在30分钟到2小时之间，并包含 newCollectTime
            int durationMinutes = random.nextInt(91) + 30; // 30 到 120 分钟
            int beforeCollectMinutes = random.nextInt(durationMinutes + 1); // 确定 collectTime 在时间段内的位置

            LocalDateTime newStartTime = newCollectTime.minusMinutes(beforeCollectMinutes);
            LocalDateTime newEndTime = newStartTime.plusMinutes(durationMinutes);

            logs.setStartTime(Date.from(newStartTime.atZone(ZoneId.systemDefault()).toInstant()));
            logs.setEndTime(Date.from(newEndTime.atZone(ZoneId.systemDefault()).toInstant()));
        }
        // 1. 先执行巡查记录的批量插入
        int result = patrolInspectionLogsMapper.batchInsert(logsList);

        // 2. 收集所有需要查询的用户ID
        Set<Long> allUserIds = logsList.stream()
                .map(PatrolInspectionLogs::getUserIds)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toSet());

        if (allUserIds.isEmpty()) {
            return result;
        }

        // 3. 一次性查询所有用户信息
        Map<Long, SysUser> userMap = remoteUserService.findListParam(new ArrayList<>(allUserIds))
                .getData()
                .stream()
                .collect(Collectors.toMap(
                        SysUser::getUserId,
                        user -> user,
                        (existing, replacement) -> existing
                ));

        // 4. 构建巡查人员关联数据
        List<PatrolInspectionUser> userList = logsList.stream()
                .filter(log -> log.getUserIds() != null && !log.getUserIds().isEmpty())
                .flatMap(log -> log.getUserIds().stream()
                        .map(userId -> {
                            SysUser sysUser = userMap.get(userId);
                            if (sysUser == null) {
                                return null;
                            }
                            PatrolInspectionUser user = new PatrolInspectionUser();
                            user.setPatrolId(log.getId());
                            user.setUserId(userId);
                            user.setNickName(sysUser.getNickName());
                            user.setSignId(sysUser.getSignId());
                            return user;
                        })
                )
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 5. 批量插入巡查人员关联数据
        if (!userList.isEmpty()) {
            patrolInspectionUserService.batchInsert(userList);
        }

        return result;
    }

    /**
     * 批量删除巡查记录
     *
     * @param ids 巡查记录ID
     * @return 结果
     */
    @Override
    public int deleteByIds(List<String> ids) {
        // 1. 删除巡查记录
        int result = patrolInspectionLogsMapper.batchDelete(ids);

        // 2. 删除巡查人员关联数据
        patrolInspectionUserService.batchDeleteByPatrolIds(ids);

        return result;
    }

    // 创建空的巡查统计VO
    private InspectionStatsVO assembleEmptyInspectionStatsVO() {
        return assembleInspectionStatsVO(
                createEmptyRouteStats(),
                createEmptyAssetStats(),
                createEmptyAssetStats(),
                createEmptyAssetStats(),
                createEmptyAssetStats()
        );
    }

    /**
     * 设置巡查日志的用户信息和签名URL
     * <p>
     * 处理流程:
     * 1. 收集所有巡查日志ID
     * 2. 批量查询关联的用户信息
     * 3. 收集所有需要查询的签名ID
     * 4. 批量获取签名URL
     * 5. 为每个巡查日志设置用户信息和头像
     *
     * @param logsList 巡查日志列表
     */
    @Override
    public void setUserInfoAndSignUrl(List<PatrolInspectionLogs> logsList) {
        setUserInfoAndSignUrl(logsList, true);
    }

    /**
     * 设置巡查日志的用户信息和签名URL
     * <p>
     * 处理流程:
     * 1. 收集所有巡查日志ID
     * 2. 批量查询关联的用户信息
     * 3. 收集所有需要查询的签名ID
     * 4. 批量获取签名URL
     * 5. 为每个巡查日志设置用户信息和头像
     *
     * @param logsList  巡查日志列表
     * @param firstOnly 是否只设置第一个用户的签名URL
     */
    public void setUserInfoAndSignUrl(List<PatrolInspectionLogs> logsList, boolean firstOnly) {
        if (CollectionUtil.isEmpty(logsList)) {
            return;
        }

        // 1. 收集所有巡查日志ID
        List<String> patrolIds = logsList.stream()
                .map(PatrolInspectionLogs::getId)
                .collect(Collectors.toList());

        // 2. 批量查询关联的用户信息
        QueryWrapper<PatrolInspectionUser> userWrapper = new QueryWrapper<>();
        userWrapper.in("patrol_id", patrolIds);
        List<PatrolInspectionUser> userList = patrolInspectionUserService.list(userWrapper);

        // 3. 按巡查ID分组用户信息
        Map<String, List<PatrolInspectionUser>> userMap = userList.stream()
                .collect(Collectors.groupingBy(PatrolInspectionUser::getPatrolId));

        // 4. 收集所有需要查询的签名ID
        Set<String> signIds = userList.stream()
                .map(PatrolInspectionUser::getSignId)
                .filter(Objects::nonNull)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        // 5. 批量查询文件信息
        final Map<String, String> signIdToUrlMap = new HashMap<>();
        if (!signIds.isEmpty()) {
            try {
                R<List<SysFile>> filesResult = remoteFileService.findFilesByOwnerIds(new ArrayList<>(signIds));
                if (filesResult.getCode() == 200 && filesResult.getData() != null) {
                    signIdToUrlMap.putAll(filesResult.getData().stream()
                            .collect(Collectors.toMap(
                                    file -> file.getId(),
                                    file -> file.getUrl(),
                                    (v1, v2) -> v1
                            )));
                }
            } catch (Exception e) {
                log.error("批量获取文件URL失败, signIds: {}", signIds, e);
            }
        }

        // 6. 为每个巡查日志设置用户信息和头像
        List<CompletableFuture<Void>> futures = logsList.stream()
                .map(patrolLog -> CompletableFuture.runAsync(() -> {
                    List<PatrolInspectionUser> users = userMap.getOrDefault(patrolLog.getId(), Collections.emptyList());
                    if (!users.isEmpty()) {
                        patrolLog.setUserIds(users.stream().map(PatrolInspectionUser::getUserId).collect(Collectors.toList()));
                        patrolLog.setUserNameList(users.stream().map(PatrolInspectionUser::getNickName).collect(Collectors.toList()));
                        patrolLog.setNickNameList(users.stream().map(PatrolInspectionUser::getNickName).collect(Collectors.toList()));
                        patrolLog.setSignIdList(users.stream().map(PatrolInspectionUser::getSignId).collect(Collectors.toList()));

                        if (firstOnly) {
                            // 寻找第一个有签名的用户
                            String url = users.stream()
                                    .filter(user -> StringUtils.isNotBlank(user.getSignId()))
                                    .findFirst()
                                    .map(user -> signIdToUrlMap.get(user.getSignId()))
                                    .orElseGet(() -> {
                                        try {
                                            ImageTextRequest imageTextRequest = new ImageTextRequest();
                                            imageTextRequest.setNameList(patrolLog.getNickNameList());
                                            CompletableFuture<R<String>> future = CompletableFuture.supplyAsync(() ->
                                                    remoteFileService.mergeImagesAndTexts(imageTextRequest));
                                            R<String> r = future.get(5, TimeUnit.SECONDS);
                                            if (r.getCode() == 200) {
                                                return r.getData();
                                            }
                                        } catch (Exception e) {
                                            log.error("生成默认头像失败, patrolId: {}", patrolLog.getId(), e);
                                        }
                                        return null;
                                    });
                            patrolLog.setOwnerAvatar(url);
                        } else {
                            try {
                                ImageTextRequest imageTextRequest = new ImageTextRequest();
                                List<String> ownerIdList = new ArrayList<>();
                                List<String> nameList = new ArrayList<>();
                                for (PatrolInspectionUser user : users) {
                                    String ownerId = user.getSignId();
                                    if (StringUtils.isNotBlank(ownerId)) {
                                        ownerIdList.add(ownerId);
                                    } else {
                                        nameList.add(user.getNickName());
                                    }
                                }
                                imageTextRequest.setOwnerIdList(ownerIdList);
                                imageTextRequest.setNameList(nameList);
                                CompletableFuture<R<String>> future = CompletableFuture.supplyAsync(() ->
                                        remoteFileService.mergeImagesAndTexts(imageTextRequest));
                                R<String> r = future.get(30, TimeUnit.SECONDS);
                                if (r.getCode() == 200) {
                                    patrolLog.setOwnerAvatar(r.getData());
                                }
                            } catch (Exception e) {
                                log.error("生成默认头像失败, patrolId: {}", patrolLog.getId(), e);
                                patrolLog.setOwnerAvatar(null);
                            }
                        }
                    }
                }))
                .collect(Collectors.toList());
        // 等待所有任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(600, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("处理巡查日志用户信息和头像失败", e);
        }
    }

    /**
     * 保存巡查日志（包含巡查人员）
     *
     * @param patrolInspectionLogs 巡查日志信息
     * @return 巡查日志
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PatrolInspectionLogs savePatrolInspectionLogs(PatrolInspectionLogs patrolInspectionLogs) {
        List<Long> userIds = patrolInspectionLogs.getUserIds();
        if (userIds == null || userIds.isEmpty()) {
            throw new RuntimeException("巡查人员不能为空");
        }

        // 设置默认时间
        Date date = new Date();
        if (patrolInspectionLogs.getCollectTime() == null) {
            patrolInspectionLogs.setCollectTime(date);
        }
        if (patrolInspectionLogs.getReportedTime() == null) {
            patrolInspectionLogs.setReportedTime(date);
        }

        // 添加巡查机构
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null) {
            SysDept dept = loginUser.getSysUser().getDept();
            patrolInspectionLogs.setPatrolUnitId(dept.getDeptId());
            patrolInspectionLogs.setPatrolUnitName(dept.getDeptName());
        }

        // 处理维护单位信息
        String maintenanceUnitId = patrolInspectionLogs.getMaintenanceUnitId();
        if (StringUtils.isNotBlank(maintenanceUnitId) && !maintenanceUnitId.equals("3")) {
            SysDept sysDept = remoteDeptAuthService.getBranchOfficeByDeptId(Long.parseLong(maintenanceUnitId)).getData();
            if (sysDept != null) {
                patrolInspectionLogs.setMaintenanceUnitId(sysDept.getDeptId().toString());
                patrolInspectionLogs.setMaintenanceUnitName(sysDept.getDeptName());
            }
        }

        // 保存巡查日志
        this.save(patrolInspectionLogs);

        // 获取用户信息并保存巡查人员关联
        List<SysUser> sysUsers = remoteUserService.findListParam(userIds).getData();
        Map<Long, SysUser> userMap = sysUsers.stream().collect(Collectors.toMap(SysUser::getUserId, sysUser -> sysUser));

        List<PatrolInspectionUser> userList = new ArrayList<>();
        for (Long userId : userIds) {
            SysUser sysUser = userMap.get(userId);
            if (sysUser != null) {
                PatrolInspectionUser user = new PatrolInspectionUser();
                user.setPatrolId(patrolInspectionLogs.getId());
                user.setUserId(userId);
                user.setNickName(sysUser.getNickName());
                user.setSignId(sysUser.getSignId());
                userList.add(user);
            }
        }

        if (!userList.isEmpty()) {
            patrolInspectionUserService.saveBatch(userList);
        }

        return patrolInspectionLogs;
    }

    /**
     * 更新巡查日志（包含巡查人员）
     *
     * @param patrolInspectionLogs 巡查日志信息
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePatrolInspectionLogs(PatrolInspectionLogs patrolInspectionLogs) {
        // 1. 验证用户IDs
        List<Long> userIds = patrolInspectionLogs.getUserIds();
        if (userIds == null || userIds.isEmpty()) {
            throw new RuntimeException("巡查人员不能为空");
        }

        // 2. 更新巡查日志基本信息
        boolean updateResult = this.updateById(patrolInspectionLogs);
        if (!updateResult) {
            return false;
        }

        // 3. 获取用户信息
        List<SysUser> sysUsers = remoteUserService.findListParam(userIds).getData();
        Map<Long, SysUser> userMap = sysUsers.stream().collect(Collectors.toMap(SysUser::getUserId, sysUser -> sysUser));

        // 4. 删除旧的巡查人员关联
        patrolInspectionUserService.removeByMap(Paramap.create().put("patrol_id", patrolInspectionLogs.getId()));

        // 5. 创建新的巡查人员关联
        List<PatrolInspectionUser> userList = new ArrayList<>();
        for (Long userId : userIds) {
            SysUser sysUser = userMap.get(userId);
            if (sysUser != null) {
                PatrolInspectionUser user = new PatrolInspectionUser();
                user.setPatrolId(patrolInspectionLogs.getId());
                user.setUserId(userId);
                user.setNickName(sysUser.getNickName());
                user.setSignId(sysUser.getSignId());
                userList.add(user);
            }
        }

        // 6. 保存巡查人员关联
        if (!userList.isEmpty()) {
            patrolInspectionUserService.saveBatch(userList);
        }

        return true;
    }

    @Override
    public AssetStatsDTO getAssetStats(Integer type) {
        // 验证用户登录
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            throw new RuntimeException("获取当前用户失败");
        }
        InspectionType inspectionType = switch (type) {
            case 1 -> InspectionType.BRIDGE_REGULAR_INSPECTION;
            case 2 -> InspectionType.TUNNEL_REGULAR_INSPECTION;
            case 3 -> InspectionType.CULVERT_REGULAR_INSPECTION;
            default -> null;
        };
        AssetBaseDataRequest dataRequest = AssetBaseDataRequest.builder()
                .type(inspectionType)
                .checkTimeHas(LocalDateTime.now())
                .dataRule(true)
                .build();
        baseCacheService.setDeptIds(dataRequest);
        dataRequest.setDataRule(false);
        AssetStatsDTO culvertStats;
        try {
            culvertStats = calculateAssetStats(dataRequest);
        } catch (Exception e) {
            log.error("计算统计数据失败", e);
            culvertStats = createEmptyAssetStats();
        }
        return culvertStats;
    }

    @Override
    public List<AssetOftenPatrolVO> getDeptAssetOftenCountList(Integer type) {
        // 验证用户登录
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            throw new RuntimeException("获取当前用户失败");
        }
        InspectionType inspectionType = switch (type) {
            case 1 -> InspectionType.BRIDGE_REGULAR_INSPECTION;
            case 2 -> InspectionType.TUNNEL_REGULAR_INSPECTION;
            case 3 -> InspectionType.CULVERT_REGULAR_INSPECTION;
            default -> null;
        };
        AssetBaseDataRequest dataRequest = AssetBaseDataRequest.builder()
                .type(inspectionType)
                .checkTimeHas(LocalDateTime.now())
                .dataRule(true)
                .build();
        baseCacheService.setDeptIds(dataRequest);
        dataRequest.setDataRule(false);

        List<BaseDataDomainWithDistance<BaseDataCache>> assets =
                baseCacheService.listBy(dataRequest, null, null, new AtomicInteger());

        Map<String, AssetOftenPatrolVO> collect = assets.stream()
                .filter(asset -> asset != null && asset.getBaseData() != null)
                .filter(asset -> asset.getBaseData().getManagementMaintenanceId() != null)
                .collect(Collectors.groupingBy(
                        asset -> asset.getBaseData().getManagementMaintenanceId(),  // 直接获取ID，因为已经确保不为null
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                assetList -> {
                                    long inspectedCount = assetList.stream()
                                            .filter(BaseDataDomainWithDistance::getIsCheck)
                                            .count();
                                    BaseDataCache baseData = assetList.get(0).getBaseData();
                                    return AssetOftenPatrolVO.builder()
                                            .checkedNum((int) inspectedCount)
                                            .toBeCheckedNum((int) (assetList.size() - inspectedCount))
                                            .checkedAccountFor(BigDecimal.valueOf(inspectedCount).divide(BigDecimal.valueOf(assetList.size()), 6, RoundingMode.HALF_UP))
                                            .deptId(Long.valueOf(baseData.getManagementMaintenanceId()))
                                            .deptName(baseData.getManagementMaintenanceName())
                                            .build();
                                }
                        )
                ));
        return collect.values().stream()
                .sorted(Comparator.comparing(AssetOftenPatrolVO::getCheckedNum).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public List<InspectionGroupCountVO> deptPatrolMileageCount(Integer year, Integer type) {
        // 默认年
        LocalDateTime startTime = LocalDateTime.of(year, 1, 1, 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(year + 1, 1, 1, 0, 0, 0);
        if (type == 2) {
            // 取上个月
            int monthValue = LocalDateTime.now().minusMonths(1).getMonthValue();
            startTime = LocalDateTime.of(year, monthValue, 1, 0, 0, 0);
            endTime = LocalDateTime.of(year, monthValue + 1, 1, 0, 0, 0);
        }
        // 资产权限
        List<String> maintenanceIdList = remoteMaintenanceSectionService.findUserMaintenanceIds(getLoginUser().getUserid()).getData();
        List<PatrolInspectionLogs> logsList = patrolInspectionLogsMapper.findListByUserAndTime(maintenanceIdList, startTime, endTime, null);
        if (!logsList.isEmpty()) {
            List<Long> deptIdList = logsList.stream().map(PatrolInspectionLogs::getMaintenanceUnitId).filter(Objects::nonNull)
                    .collect(Collectors.toSet())
                    .stream().map(Long::parseLong).collect(Collectors.toList());
            List<SysDept> deptList = remoteDeptAuthService.getByIdList(deptIdList).getData();
            List<InspectionGroupCountVO> inspectionGroupCountVOList = new ArrayList<>();
            for (PatrolInspectionLogs logs : logsList) {
                for (SysDept dept : deptList) {
                    if (logs.getMaintenanceUnitId() != null && dept.getDeptId().equals(Long.valueOf(logs.getMaintenanceUnitId()))) {
                        if (dept.getDeptType() == 3 || com.ruoyi.common.core.utils.StringUtils.isNotEmpty(dept.getAdministrateOfficeId())) {
                            InspectionGroupCountVO inspectionCountVO = new InspectionGroupCountVO();
                            inspectionCountVO.setDeptId(dept.getDeptType() == 3 ? dept.getDeptId() : Long.valueOf(dept.getAdministrateOfficeId()));
                            inspectionCountVO.setDeptName(dept.getDeptType() == 3 ? dept.getDeptName() : dept.getParentName());
                            inspectionCountVO.setRoadbedMileage(Optional.ofNullable(logs.getPatrolMileage()).orElse(BigDecimal.ZERO)
                                    .divide(BigDecimal.valueOf(1000), 3, RoundingMode.HALF_UP));
                            inspectionGroupCountVOList.add(inspectionCountVO);
                            break;
                        }
                    }
                }
            }
            // 按 deptId 分组，并累加 roadbedMileage
            Map<Long, InspectionGroupCountVO> groupedResult = inspectionGroupCountVOList.stream()
                    .collect(Collectors.groupingBy(InspectionGroupCountVO::getDeptId,
                            Collectors.reducing(
                                    new InspectionGroupCountVO(null, null, BigDecimal.ZERO),
                                    (vo1, vo2) -> {
                                        Long deptId = vo1.getDeptId() == null ? vo2.getDeptId() : vo1.getDeptId();
                                        String deptName = vo1.getDeptName() == null ? vo2.getDeptName() : vo1.getDeptName();
                                        BigDecimal totalMileage = vo1.getRoadbedMileage().add(vo2.getRoadbedMileage());
                                        return new InspectionGroupCountVO(deptId, deptName, totalMileage);
                                    }
                            )
                    ));
            // 将分组结果转换为列表
            return groupedResult.values().stream().toList();
        } else {
            return new ArrayList<>();
        }
    }


    @Override
    public List<MileageCountVO> getMaintenanceSectionIdMileage(Integer year, String maintenanceSectionId) {
        // 默认年
        LocalDateTime startTime = LocalDateTime.of(year, 1, 1, 0, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(year + 1, 1, 1, 0, 0, 0);
        // 资产权限
        List<String> maintenanceIdList = new ArrayList<>();
        if (maintenanceSectionId == null || maintenanceSectionId.isEmpty()) {
            maintenanceIdList = remoteMaintenanceSectionService.findUserMaintenanceIds(getLoginUser().getUserid()).getData();
        } else {
            maintenanceIdList.add(maintenanceSectionId);
        }
        List<PatrolInspectionLogs> logsList = patrolInspectionLogsMapper.findListByUserAndTime(maintenanceIdList, startTime, endTime, null);
        if (!logsList.isEmpty()) {
            // 按 deptId 分组，并累加 roadbedMileage
            Map<String, MileageCountVO> result = logsList.stream()
                    .collect(Collectors.toMap(
                            PatrolInspectionLogs::getMaintenanceSectionId,  // 分组键
                            vo -> MileageCountVO.builder()
                                    .maintenanceSectionId(vo.getMaintenanceSectionId())
                                    .maintenanceSectionName(vo.getMaintenanceSectionName())
                                    .roadbedMileage(vo.getPatrolMileage())
                                    .build(),  // 直接复制对象作为初始值
                            (vo1, vo2) -> {  // 合并逻辑
                                vo1.setRoadbedMileage(
                                        Optional.ofNullable(vo1.getRoadbedMileage()).orElse(BigDecimal.ZERO)
                                                .add(Optional.ofNullable(vo2.getRoadbedMileage()).orElse(BigDecimal.ZERO))
                                );
                                return vo1;
                            }
                    ));
            // 将分组结果转换为列表
            return result.values().stream().toList();
        } else {
            return new ArrayList<>();
        }
    }


    @Override
    public List<PatrolInspectionLogs> exportReportCard(Map<String, Object> params, Map<String, String> signUrlMap) {
        Integer isEvent = MapUtil.getInt(params, "isEvent");
        List<String> ids = Convert.toList(String.class, params.get("ids"));
        if (null != isEvent && ids == null) {
            List<String> idsDiseaseIdList = remoteRoadDiseaseService.getRoadDiseaseIdList().getData();
            if (isEvent == 1) params.put("idList", idsDiseaseIdList);
            else params.put("idListNot", idsDiseaseIdList);
        }
//        R<List<String>> r = remoteMaintenanceSectionService.findUserMaintenanceIds(null);
//        if (r.getCode() != 200) {
//            return new ArrayList<>();
//        }
//        params.put("sectionIdList", r.getData());
        params.put("order", "l.collect_time desc");
//        List<PatrolInspectionLogs> list = findListByParam(params);

        List<PatrolInspectionLogs> list = patrolInspectionLogsMapper.findListByUserWithOrderedSigns(params);

        if (CollectionUtil.isEmpty(list)) {
            return list; // 如果没有找到日志，提前返回
        }

        // 处理签名相关信息
        Set<String> names = new HashSet<>();
        Set<String> ids2 = new HashSet<>();

        // 从PatrolInspectionLogs中收集用户签名和ID信息
        for (PatrolInspectionLogs log : list) {
            if(log.getUserNames().isEmpty() && log.getSignIds().isEmpty() && !log.getPersonStr().isEmpty()){
                String personStr = log.getPersonStr();
                String processedPersonStr;

                    // Delimiters in order of preference.
                    // For String.split, which takes a regex, backslash needs to be escaped.
                    String[] delimiters = {" ", ",", "/", "\\\\", "、", "-"};
                    java.util.List<String> parts = null;

                    for (String delimiterRegex : delimiters) {
                        // Use split(regex, limit) where limit = -1 to include trailing empty strings.
                        // This makes length > 1 a reliable indicator that the delimiter was found and used.
                        String[] currentParts = personStr.split(delimiterRegex, -1);
                        if (currentParts.length > 1) {
                            parts = java.util.Arrays.asList(currentParts);
                            break;
                        }
                    }

                    if (parts != null) {
                        // Join the parts with a comma.
                        processedPersonStr = String.join(",", parts);
                    } else {
                        // No delimiter caused a split, so use the original string.
                        processedPersonStr = personStr;
                    }
                log.setUserNames(processedPersonStr);
            }
            // 处理用户名称
            if (log.getUserNames() != null && !log.getUserNames().isBlank()) {
                List<String> nameItems = Arrays.stream(log.getUserNames().split(","))
                        .map(String::trim)
                        .filter(s -> s != null && !s.isBlank() && !"null".equalsIgnoreCase(s))
                        .collect(Collectors.toList());
                names.addAll(nameItems);
            }

            // 处理签名ID
            if (log.getSignIds() != null && !log.getSignIds().isBlank()) {
                List<String> signItems = Arrays.stream(log.getSignIds().split(","))
                        .map(String::trim)
                        .filter(s -> s != null && !s.isBlank() && !"null".equalsIgnoreCase(s))
                        .collect(Collectors.toList());
                ids2.addAll(signItems);
            }

            // 新增逻辑：处理 userNames 和 signIds 以填充 signNameList
            List<String> resultSignNameList = new ArrayList<>();
            String currentSignIds = log.getSignIds(); // 使用 log 对象中当前最新的值
            String currentUserNames = log.getUserNames(); // 使用 log 对象中当前最新的值

            boolean signIsEffectivelyBlank = (currentSignIds == null || currentSignIds.isBlank() || "null".equalsIgnoreCase(currentSignIds.trim()));
            boolean nameIsEffectivelyBlank = (currentUserNames == null || currentUserNames.isBlank() || "null".equalsIgnoreCase(currentUserNames.trim()));

            if (signIsEffectivelyBlank && nameIsEffectivelyBlank) {
                // 两者都无效或为空，resultSignNameList 保持为空
            } else if (signIsEffectivelyBlank && !nameIsEffectivelyBlank) {
                // 只有 name 有效
                // 注意：PatrolAssetCheckProcessor.java 中的 splitAndFilter 是 public static
                // 我们将在 PatrolInspectionLogsServiceImpl 中添加一个类似的 private static 方法
                List<String> nameItems = splitAndFilter(currentUserNames);
                resultSignNameList.addAll(nameItems);
            } else if (!signIsEffectivelyBlank && nameIsEffectivelyBlank) {
                // 只有 sign 有效
                List<String> signItems = splitAndFilter(currentSignIds);
                resultSignNameList.addAll(signItems);
            } else {
                // 两者都可能包含有效数据
                // 注意：这里直接分割，未再次调用 splitAndFilter，因为该方法主要用于过滤"null"等无效项
                // 而这里的 userNames 和 signIds 已经是经过初步处理的
                List<String> signList = Arrays.stream(currentSignIds.split(","))
                        .map(String::trim)
                        .collect(Collectors.toList());
                List<String> nameList = Arrays.stream(currentUserNames.split(","))
                        .map(String::trim)
                        .collect(Collectors.toList());

                if (signList.size() != nameList.size()) {
                    // 长度不等，优先使用 signIds (经过 splitAndFilter 处理以确保纯净)
                    resultSignNameList.addAll(splitAndFilter(currentSignIds));
                } else {
                    // 长度相等，逐个配对处理
                    for (int i = 0; i < signList.size(); i++) {
                        String signToken = signList.get(i);
                        String nameToken = nameList.get(i);

                        boolean isSignTokenInvalid = (signToken == null || signToken.isBlank() || "null".equalsIgnoreCase(signToken));
                        boolean isNameTokenValid = (nameToken != null && !nameToken.isBlank() && !"null".equalsIgnoreCase(nameToken));

                        if (isSignTokenInvalid) {
                            if (isNameTokenValid) {
                                resultSignNameList.add(nameToken); // sign 无效，name 有效，使用 name
                            }
                            // 如果 sign 无效且 name 也无效，则此项忽略
                        } else {
                            resultSignNameList.add(signToken); // sign 有效，使用 sign
                        }
                    }
                }
            }
            log.setSignNameList(resultSignNameList);
            // 新增逻辑结束
        }

        // 获取签名URL
        if (!names.isEmpty() || !ids2.isEmpty()) {
            ImageTextRequest imageTextRequest = new ImageTextRequest();
            imageTextRequest.setOwnerIdList(new ArrayList<>(ids2));
            imageTextRequest.setNameList(new ArrayList<>(names));
            R<Map<String, String>> resultMap = remoteFileService.getUserSign(imageTextRequest);

            if (resultMap.getCode() == 200 && resultMap.getData() != null) {
                signUrlMap.putAll(resultMap.getData());
            } else {
                log.error("获取签名失败");
            }
        }

        // 设置签名URL到对象中
        if (!signUrlMap.isEmpty()) {
            for (PatrolInspectionLogs log : list) {
                setSignUrl(log, signUrlMap);
            }
        }

        List<String> logIds = list.stream()
                .map(PatrolInspectionLogs::getId)
                .collect(Collectors.toList());

        Map<String, List<RemoteRoadDiseaseResponse>> diseaseMap = Collections.emptyMap();
        if (CollectionUtil.isNotEmpty(logIds)) {
            // --- 开始并行获取数据 ---
            int partitionCount = 8; // 分区数量
            int totalSize = logIds.size();
            int batchSize = (totalSize + partitionCount - 1) / partitionCount;

            List<CompletableFuture<R<List<RemoteRoadDiseaseResponse>>>> futures = new ArrayList<>();
            // 使用通用线程池或注入特定的执行器服务
            Executor executor = this.taskExecutor;

            for (int i = 0; i < totalSize; i += batchSize) {
                int end = Math.min(i + batchSize, totalSize);
                List<String> sublist = logIds.subList(i, end);
                if (!sublist.isEmpty()) {
                    CompletableFuture<R<List<RemoteRoadDiseaseResponse>>> future = CompletableFuture.supplyAsync(
                            () -> remoteRoadDiseaseService.getRoadDiseaseListByIdList(sublist), executor);
                    futures.add(future);
                }
            }

            // 等待所有任务完成
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            } catch (Exception e) {
                log.error("等待并行获取道路病害数据任务完成时出错", e);
                // 决定如何处理：可能返回没有病害信息的列表，或抛出异常
                // 目前，继续处理可能的部分数据
            }


            // 收集结果
            List<RemoteRoadDiseaseResponse> combinedDiseaseList = new ArrayList<>();
            for (CompletableFuture<R<List<RemoteRoadDiseaseResponse>>> future : futures) {
                try {
                    R<List<RemoteRoadDiseaseResponse>> result = future.get(); // 获取结果，可能抛出异常
                    if (result != null && result.getCode() == 200 && CollectionUtil.isNotEmpty(result.getData())) {
                        combinedDiseaseList.addAll(result.getData());
                    } else {
                        log.warn("获取道路病害数据分区失败或分区未返回数据。结果: {}", result);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("获取道路病害数据分区时被中断", e);
                } catch (ExecutionException e) {
                    log.error("执行道路病害数据获取时出错", e.getCause());
                } catch (Exception e) {
                    log.error("获取道路病害数据分区时发生意外错误", e);
                }
            }

            // 分组合并结果
            if (CollectionUtil.isNotEmpty(combinedDiseaseList)) {
                diseaseMap = combinedDiseaseList.stream()
                        .filter(Objects::nonNull) // 为安全起见添加空检查
                        .collect(Collectors.groupingBy(RemoteRoadDiseaseResponse::getRecordId));
            }
            // --- 结束并行获取数据 ---
        }

        // 使用新的diseaseMap将病害与日志关联的原始逻辑
        // 在访问之前确保diseaseMap不为空
        final Map<String, List<RemoteRoadDiseaseResponse>> finalDiseaseMap = diseaseMap;
        for (PatrolInspectionLogs logs : list) {
            if (finalDiseaseMap != null) {
                List<RemoteRoadDiseaseResponse> remoteRoadDiseaseResponses = finalDiseaseMap.get(logs.getId());
                if (remoteRoadDiseaseResponses != null) {
                    logs.setRoadDiseaseList(remoteRoadDiseaseResponses);
                }
            }
        }
        return list;
    }

    /**
     * 设置签名URL到巡检日志中
     *
     * @param log        巡检日志对象
     * @param signUrlMap 签名URL映射
     */
    private void setSignUrl(PatrolInspectionLogs log, Map<String, String> signUrlMap) {
        if (log == null || signUrlMap == null || signUrlMap.isEmpty()) {
            return;
        }

        List<String> userSignUrls = new ArrayList<>();

        // 根据signIds设置签名URL
        if (log.getSignIds() != null && !log.getSignIds().isBlank()) {
            List<String> signIds = Arrays.stream(log.getSignIds().split(","))
                    .map(String::trim)
                    .filter(s -> s != null && !s.isBlank() && !"null".equalsIgnoreCase(s))
                    .collect(Collectors.toList());

            for (String signId : signIds) {
                String signUrl = signUrlMap.get(signId);
                if (signUrl != null && !signUrl.isBlank()) {
                    userSignUrls.add(signUrl);
                }
            }
        }

        // 设置签名URL到对象
        log.setUserSignUrlList(userSignUrls);
        if (!userSignUrls.isEmpty()) {
            log.setSignUrlStr(String.join(",", userSignUrls));
        }
    }

    private List<PatrolInspectionLogsTotalVO> fillUnitName(List<PatrolInspectionLogsTotalVO> patrolInspectionLogsTotalVOList) {
        List<SysDept> data = remoteDeptAuthService.list(new SysDept()).getData();
        if (CollectionUtil.isNotEmpty(data)) {
            for (PatrolInspectionLogsTotalVO temp : patrolInspectionLogsTotalVOList) {
                List<SysDept> list = data.stream().filter(item -> item.getDeptName().equals(temp.getMaintenanceSubUnitName())).toList();
                if (CollectionUtil.isNotEmpty(list)) {
                    temp.setMaintenanceUnitName(list.get(0).getParentName());
                }
            }
        }

        return patrolInspectionLogsTotalVOList;
    }


    public List<PatrolInspectionLogsTotalVO> fillOtherFields(List<PatrolInspectionLogsTotalVO> patrolInspectionLogsTotalVOList) throws InterruptedException, ExecutionException {
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        patrolInspectionLogsTotalVOList.forEach(item -> {

            CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> {
                List<RemoteRoadDiseaseResponse> data = remoteRoadDiseaseService.getRoadDiseaseListByIdList(Arrays.asList(item.getIds().split(","))).getData();
                item.setDiseaseNum("0");
                if (CollectionUtil.isNotEmpty(data)) {
                    item.setDiseaseNum(String.valueOf(data.size()));
                }
            });

            futures.add(future1);

            CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
                AssetBaseDataRequest request = new AssetBaseDataRequest();
                request.setType(InspectionType.BRIDGE_DAILY_INSPECTION);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                request.setCheckStartTime(LocalDateTime.parse(item.getStartTime() + " 00:00:00", formatter));
                request.setCheckEndTime(LocalDateTime.parse(item.getStartTime() + " 23:59:59", formatter));
                request.setCustomSqlCondition("pac.maintenance_section_name = '" + item.getMaintenanceSectionName() + "'");
//                baseCacheService.setDeptIds(request);
                AtomicInteger total = new AtomicInteger();
                List<PatrolAssetCheck> patrolAssetChecks = patrolAssetCheckService.selectPatrolAssetCheck(request, null, null, total);
                item.setBridgeRecordNum("0/" + String.valueOf(total));

            });
            futures.add(future2);

            CompletableFuture<Void> future3 = CompletableFuture.runAsync(() -> {
                AssetBaseDataRequest request = new AssetBaseDataRequest();
                request.setType(InspectionType.TUNNEL_DAILY_INSPECTION);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                request.setCheckStartTime(LocalDateTime.parse(item.getStartTime() + " 00:00:00", formatter));
                request.setCheckEndTime(LocalDateTime.parse(item.getStartTime() + " 23:59:59", formatter));
                request.setCustomSqlCondition("pac.maintenance_section_name = '" + item.getMaintenanceSectionName() + "'");
                AtomicInteger total = new AtomicInteger();
                List<PatrolAssetCheck> patrolAssetChecks = patrolAssetCheckService.selectPatrolAssetCheck(request, null, null, total);
                item.setTunnelRecordNum("0/" + String.valueOf(total));

            });
            futures.add(future3);

        });

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();


//        return fillUnitName(patrolInspectionLogsTotalVOList);
        return patrolInspectionLogsTotalVOList;
    }


    @Override
    public List<PatrolInspectionLogsTotalVO> countByPatrolUnit(Map<String, Object> params) throws ExecutionException, InterruptedException {

        List<PatrolInspectionLogsTotalVO> patrolInspectionLogsTotalVOList = patrolInspectionLogsMapper.countByPatrolUnit(params);
//        fillOtherFields(patrolInspectionLogsTotalVOList);
        return fillUnitName(fillOtherFields(patrolInspectionLogsTotalVOList));
    }


    @Override
    public List<PatrolInspectionLogsTotalVO> countByMaintenanceSubUnit(Map<String, Object> params) throws ExecutionException, InterruptedException {

        List<PatrolInspectionLogsTotalVO> patrolInspectionLogsTotalVOList = patrolInspectionLogsMapper.countByMaintenanceSubUnit(params);
//        fillOtherFields(patrolInspectionLogsTotalVOList);
        return fillUnitName(fillOtherFields(patrolInspectionLogsTotalVOList));
    }


    @Override
    public List<PatrolInspectionLogsTotalVO> countByMaintenanceUnit(Map<String, Object> params) {
        List<PatrolInspectionLogsTotalVO> patrolInspectionLogsTotalVOList = patrolInspectionLogsMapper.countByMaintenanceSubUnit(params);
        fillUnitName(patrolInspectionLogsTotalVOList);

        // 三级分组并聚合
        Map<String, Map<String, Map<String, PatrolInspectionLogsTotalVO>>> result = patrolInspectionLogsTotalVOList.stream()
                .filter(record ->
                        record.getMaintenanceSectionName() != null &&  // 过滤掉关键字段为 null 的记录
                                record.getMaintenanceUnitName() != null &&
                                record.getStartTime() != null
                )
                .collect(Collectors.groupingBy(
                        PatrolInspectionLogsTotalVO::getMaintenanceSectionName,  // 第一级：maintenanceSectionName
                        Collectors.groupingBy(
                                PatrolInspectionLogsTotalVO::getMaintenanceUnitName,  // 第二级：maintenanceUnitName
                                Collectors.groupingBy(
                                        PatrolInspectionLogsTotalVO::getStartTime,       // 第三级：startTime
                                        Collectors.collectingAndThen(
                                                Collectors.toList(),
                                                list -> {
                                                    // 聚合逻辑
                                                    PatrolInspectionLogsTotalVO aggregated = new PatrolInspectionLogsTotalVO();
                                                    aggregated.setMaintenanceSectionName(list.get(0).getMaintenanceSectionName());
                                                    aggregated.setMaintenanceUnitName(list.get(0).getMaintenanceUnitName());
                                                    aggregated.setStartTime(list.get(0).getStartTime());

                                                    // 计算 count 总和
                                                    int totalCount = list.stream()
                                                            .mapToInt(r -> r.getCount() != null ? Integer.parseInt(r.getCount()) : 0)
                                                            .sum();
                                                    aggregated.setCount(String.valueOf(totalCount));

                                                    // 计算 patrolMileage 总和
                                                    double totalMileage = list.stream()
                                                            .mapToDouble(r -> r.getPatrolMileage() != null ? Double.parseDouble(r.getPatrolMileage()) : 0.0)
                                                            .sum();
                                                    aggregated.setPatrolMileage(String.format("%.2f", totalMileage));

                                                    // 拼接 ids
                                                    String concatenatedIds = list.stream()
                                                            .map(PatrolInspectionLogsTotalVO::getIds)
                                                            .filter(Objects::nonNull)
                                                            .collect(Collectors.joining(","));
                                                    aggregated.setIds(concatenatedIds);

                                                    return aggregated;
                                                }
                                        )
                                )
                        )
                ));

        // 将嵌套的三级 Map 转换为 List（最终结果）
        List<PatrolInspectionLogsTotalVO> finalResult = result.values().stream()
                .flatMap(unitMap -> unitMap.values().stream())
                .flatMap(timeMap -> timeMap.values().stream())
                .collect(Collectors.toList());

        return finalResult;
    }


    // 新增的辅助方法
    private static List<String> splitAndFilter(String input) {
        if (input == null || input.isBlank()) {
            return Collections.emptyList();
        }
        return Arrays.stream(input.split(","))
                .map(String::trim)
                .filter(s -> s != null && !s.isBlank() && !"null".equalsIgnoreCase(s))
                .collect(Collectors.toCollection(ArrayList::new));
    }

    /**
     * 针对较差网络环境下的巡查日志新增
     * 如果当前时间是00:05分之前，则查询创建日期在昨天23:55分到00:05分之间的记录
     * 如果当前00:05分之后的则查询今天的记录，并且是正在巡查的记录
     *
     * @param patrolInspectionLogs 巡查日志信息
     * @return 巡查日志
     */
    @Override
    public PatrolInspectionLogs addPatrolInspectionLogsInPoorNetwork(PatrolInspectionLogs patrolInspectionLogs) {
        // 验证车牌号
        if (patrolInspectionLogs.getCarNum() == null || patrolInspectionLogs.getCarNum().isEmpty()) {
            throw new RuntimeException("车牌号不能为空");
        }

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        
        // 构建查询条件
        QueryWrapper<PatrolInspectionLogs> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("car_num", patrolInspectionLogs.getCarNum()); // 精确匹配车牌号
        
        // 根据当前时间设置时间查询条件
        LocalDateTime cutoffTime = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), 0, 5); // 今天的00:05
        
        if (now.isBefore(cutoffTime)) {
            // 如果当前时间在00:05之前，则查询昨天23:55到今天00:05之间的记录
            LocalDateTime yesterdayStart = LocalDateTime.of(now.minusDays(1).getYear(), now.minusDays(1).getMonth(), 
                    now.minusDays(1).getDayOfMonth(), 23, 55);
            
            queryWrapper.ge("create_time", yesterdayStart); // 大于等于昨天23:55
            queryWrapper.le("create_time", cutoffTime); // 小于等于今天00:05
        } else {
            // 如果当前时间在00:05之后，则查询今天的正在巡查的记录
            LocalDateTime todayStart = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), 0, 0);
            
            queryWrapper.ge("create_time", todayStart); // 大于等于今天00:00
            queryWrapper.eq("status", 0); // 状态为"正在巡查"
        }
        
        // 查询符合条件的记录
        List<PatrolInspectionLogs> existingLogs = this.list(queryWrapper);
        
        // 如果找到符合条件的记录，则返回第一条记录
        if (existingLogs != null && !existingLogs.isEmpty()) {
            log.info("找到匹配的巡查日志记录, ID: {}, 车牌号: {}", existingLogs.get(0).getId(), existingLogs.get(0).getCarNum());
            return existingLogs.get(0);
        }
        
        // 如果没有找到符合条件的记录，则创建新记录
        log.info("未找到匹配的巡查日志记录，车牌号: {}，创建新记录", patrolInspectionLogs.getCarNum());
        
        // 默认设置为正在巡查状态
        if (patrolInspectionLogs.getStatus() == null) {
            patrolInspectionLogs.setStatus(0); // 0表示正在巡查
        }
        
        // 使用现有方法保存巡查日志
        return savePatrolInspectionLogs(patrolInspectionLogs);
    }

}
